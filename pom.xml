<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>maintain</groupId>
    <artifactId>maintain-agent</artifactId>
    <version>20180131-SNAPSHOT</version>

    <packaging>jar</packaging>


    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.0.5.RELEASE</version>
        <relativePath/>
    </parent>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
    </properties>


    <build>
        <defaultGoal>install</defaultGoal>
        <directory>${basedir}/target/libs</directory>
        <finalName>${project.artifactId}-${project.version}</finalName>
        <sourceDirectory>${basedir}/src</sourceDirectory>
        <!--把资源文件拷贝到指定目录-->
        <resources>
            <resource>
                <targetPath>./</targetPath>
                <filtering>false</filtering>
                <directory>${basedir}/src</directory>
                <includes>
                    <include>**/*.properties</include>
                    <include>**/*.html</include>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <targetPath>${basedir}/target/conf</targetPath>
                <filtering>false</filtering>
                <directory>${basedir}/conf</directory>
            </resource>
            <resource>
                <targetPath>${basedir}/target/deploy</targetPath>
                <filtering>false</filtering>
                <directory>${basedir}/deploy</directory>
            </resource>
            <resource>
                <targetPath>${basedir}/target/deploy</targetPath>
                <filtering>false</filtering>
                <directory>${basedir}/deploy/start</directory>
                <includes>
                    <include>start.exe</include>
                    <include>start_background.vbs</include>
                </includes>
            </resource>
            <resource>
                <targetPath>${basedir}/target/bat</targetPath>
                <filtering>false</filtering>
                <directory>${basedir}/bat</directory>
            </resource>
            <resource>
                <targetPath>${basedir}/target/libs</targetPath>
                <directory>${basedir}/libs</directory>
            </resource>
        </resources>
        <plugins>
            <!--此配置为了指定编译的JDK版本-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <encoding>UTF-8</encoding>
                    <compilerArguments>
                        <extdirs>${project.basedir}/libs</extdirs>
                    </compilerArguments>
                </configuration>
            </plugin>
            <!--设置resource-plugin的编码格式-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <version>2.6</version>
                <configuration>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.12.4</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!--把依赖的jar包拷贝到libs目录下-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <includeTypes>jar</includeTypes>
                    <overWriteIfNewer>true</overWriteIfNewer>
                    <overWriteSnapshots>true</overWriteSnapshots>
                    <type>jar</type>
                    <outputDirectory>${basedir}/target/libs</outputDirectory>
                </configuration>
            </plugin>
            <!--删除编译打包后的文件夹下maven生成的文件夹和执行生成icegridrun.bat/sh的脚本-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <phase>install</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <delete dir="${basedir}/target/libs/generated-test-sources" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/libs/classes" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/libs/generated-sources" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/libs/antrun" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/libs/maven-status" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/libs/maven-archiver" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/libs/test-classes" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/classes" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/test-classes" includeemptydirs="true"/>
                                <delete dir="${basedir}/target/deploy/start" includeemptydirs="true"/>
                                <exec executable="cmd" spawn="true">
                                    <arg value="/c"/>
                                    <arg value="deploy\getStartBat.bat"/>
                                    <arg value="${project.artifactId}-${project.version}.jar"/>
                                </exec>
                                <delete file="${basedir}/target/deploy/getStartBat.bat" includeemptydirs="true"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-starter-logging</artifactId>
                </exclusion>

            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-undertow</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>5.0.9.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.espc</groupId>
            <artifactId>common-project</artifactId>
            <version>20181221-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>org.yaml</groupId>
            <artifactId>snakeyaml</artifactId>
            <version>1.19</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.49</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
            <optional>true</optional>
            <exclusions>
                <exclusion>
                    <groupId>*</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore-nio</artifactId>
            <version>4.4.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.4</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
            <version>4.1.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpcore-nio</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpcore</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.8</version>
            <exclusions>
                <exclusion>
                    <artifactId>httpclient</artifactId>
                    <groupId>org.apache.httpcomponents</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>


</project>