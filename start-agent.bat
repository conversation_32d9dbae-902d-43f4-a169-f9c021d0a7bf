@echo off
echo 启动分布式运维管理系统 - Agent端
echo =====================================

cd /d "D:\code\2025072605\maintain-agent"

REM 设置JDK路径
set JAVA_HOME=D:\software\jdk8
set PATH=%JAVA_HOME%\bin;%PATH%

REM 检查JDK版本
echo 检查Java版本...
java -version

REM 设置类路径
set CLASSPATH=.;target\libs\classes;conf

echo.
echo 启动参数：
echo JAVA_HOME: %JAVA_HOME%
echo CLASSPATH: %CLASSPATH%
echo 工作目录: %CD%

echo.
echo 正在启动Agent...
echo =====================================

java -Xms256m ^
     -Xmx1024m ^
     -Dfile.encoding=UTF-8 ^
     -Duser.timezone=Asia/Shanghai ^
     -cp %CLASSPATH% ^
     com.maintain.agent.MaintainAgentMain

echo.
echo Agent已停止
pause