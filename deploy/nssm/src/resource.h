//{{NO_DEPENDENCIES}}
// Microsoft Developer Studio generated include file.
// Used by nssm.rc
//
#define IDC_STATIC (-1)
#define IDI_NSSM                        101
#define IDD_INSTALL                     102
#define IDD_REMOVE                      103
#define IDD_EDIT                        104
#define IDD_APPLICATION                 105
#define IDD_DETAILS                     106
#define IDD_LOGON                       107
#define IDD_IO                          108
#define IDD_ROTATION                    109
#define IDD_APPEXIT                     110
#define IDD_SHUTDOWN                    111
#define IDD_ENVIRONMENT                 112
#define IDD_NATIVE                      113
#define IDD_PROCESS                     114
#define IDD_DEPENDENCIES                115
#define IDD_HOOKS                       116
#define IDC_PATH                        1000
#define IDC_TAB1                        1001
#define IDC_CANCEL                      1002
#define IDC_BROWSE                      1003
#define IDC_FLAGS                       1004
#define IDC_NAME                        1005
#define IDC_REMOVE                      1007
#define IDC_METHOD_CONSOLE              1008
#define IDC_METHOD_WINDOW               1009
#define IDC_METHOD_THREADS              1010
#define IDC_METHOD_TERMINATE            1011
#define IDC_KILL_CONSOLE                1012
#define IDC_KILL_WINDOW                 1013
#define IDC_KILL_THREADS                1014
#define IDC_STDIN                       1015
#define IDC_STDOUT                      1016
#define IDC_STDERR                      1017
#define IDC_BROWSE_STDIN                1018
#define IDC_BROWSE_STDOUT               1019
#define IDC_BROWSE_STDERR               1020
#define IDC_THROTTLE                    1021
#define IDC_APPEXIT                     1022
#define IDC_RESTART_DELAY               1023
#define IDC_DIR                         1024
#define IDC_BROWSE_DIR                  1025
#define IDC_ENVIRONMENT                 1026
#define IDC_ENVIRONMENT_REPLACE         1027
#define IDC_TRUNCATE                    1028
#define IDC_ROTATE                      1029
#define IDC_ROTATE_ONLINE               1030
#define IDC_ROTATE_SECONDS              1031
#define IDC_ROTATE_BYTES_LOW            1032
#define IDC_DISPLAYNAME                 1033
#define IDC_DESCRIPTION                 1034
#define IDC_STARTUP                     1035
#define IDC_LOCALSYSTEM                 1036
#define IDC_INTERACT                    1037
#define IDC_ACCOUNT                     1038
#define IDC_USERNAME                    1039
#define IDC_PASSWORD1                   1040
#define IDC_PASSWORD2                   1041
#define IDC_PRIORITY                    1042
#define IDC_AFFINITY_ALL                1043
#define IDC_AFFINITY                    1044
#define IDC_CONSOLE                     1045
#define IDC_DEPENDENCIES                1046
#define IDC_KILL_PROCESS_TREE           1047
#define IDC_HOOK_EVENT                  1048
#define IDC_HOOK_ACTION                 1049
#define IDC_HOOK                        1050
#define IDC_BROWSE_HOOK                 1051
#define IDC_REDIRECT_HOOK               1052
#define IDC_VIRTUAL_SERVICE             1053
#define IDC_TIMESTAMP                   1054

// Next default values for new objects
// 
#ifdef APSTUDIO_INVOKED
#ifndef APSTUDIO_READONLY_SYMBOLS
#define _APS_NEXT_RESOURCE_VALUE        117
#define _APS_NEXT_COMMAND_VALUE         40001
#define _APS_NEXT_CONTROL_VALUE         1055
#define _APS_NEXT_SYMED_VALUE           101
#endif
#endif

