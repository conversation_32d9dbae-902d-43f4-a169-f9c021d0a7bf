@echo off
set fileName=start.bat
set fileName_sh=start.sh
set serverName=maintain-agent
set thisBatName=%~n0.bat
set serviceJarName=%1
set mainClass=com.maintain.agent.MaintainAgentMain

echo cd .. > target\deploy\%fileName%
echo cd .. > target\deploy\%fileName_sh%
echo title  %serverName%  >> target\deploy\%fileName%
set /p="java -Dservice-jar=%serviceJarName% -Xms1g -Xmx1g -Xmn512M   -Djava.library.path=.\libs -classpath ">>target\deploy\%fileName%<nul
set /p=".\libs\%serviceJarName%;">>target\deploy\%fileName%<nul
set /p="java -classpath ">>target\deploy\%fileName_sh%<nul
set /p="./libs/%serviceJarName%:">>target\deploy\%fileName_sh%<nul

cd target\libs
SETLOCAL ENABLEDELAYEDEXPANSION
for %%i in (*) do (
	set /p=".\libs\%%i;">>..\deploy\%fileName%<nul
	set /p="./libs/%%i:">>..\deploy\%fileName_sh%<nul
)

cd ../..
set /p=" -server -Djava.ext.dirs="%%JAVA_HOME%%\jre\lib\ext" ">>target\deploy\%fileName%<nul
set /p=" -Djava.ext.dirs="$JAVA_HOME/jre/lib/ext" ">>target\deploy\%fileName_sh%<nul
set /p="%mainClass% ">>target\deploy\%fileName%<nul
set /p="%mainClass% ">>target\deploy\%fileName_sh%<nul
set /p="%%1=%%2,./deploy/icepool.conf">>target\deploy\%fileName%
set /p="%%1=%%2,./deploy/icepool.conf">>target\deploy\%fileName_sh%
echo.>> target\deploy\%fileName%
echo.>> target\deploy\%fileName_sh%
echo exit >> target\deploy\%fileName%
echo exit >> target\deploy\%fileName_sh%