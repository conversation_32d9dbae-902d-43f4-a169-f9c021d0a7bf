cd .. && java -Xmx2g -Djava.class.path=./libs/maintain-agent-20180131-SNAPSHOT.jar:./libs/c3p0-0.9.5.jar:./libs/common-project-20181221-SNAPSHOT.jar:./libs/commons-beanutils-1.9.1.jar:./libs/commons-codec-1.10.jar:./libs/commons-collections-3.2.1.jar:./libs/commons-collections4-4.1.jar:./libs/commons-io-2.4.jar:./libs/commons-lang-2.6.jar:./libs/commons-logging-1.1.3.jar:./libs/ftplet-api-1.1.0.jar:./libs/ftpserver-core-1.1.0.jar:./libs/hamcrest-core-1.3.jar:./libs/ice-3.5.1.jar:./libs/icegrid-3.5.1.jar:./libs/jackson-annotations-2.4.2.jar:./libs/jackson-core-2.4.2.jar:./libs/jackson-core-asl-1.8.8.jar:./libs/jackson-databind-2.4.2.jar:./libs/jackson-mapper-asl-1.8.8.jar:./libs/javax.el-3.0.0.jar:./libs/javax.servlet-api-3.1.0.jar:./libs/jboss-annotations-api_1.2_spec-1.0.2.Final.jar:./libs/jboss-logging-3.2.1.Final.jar:./libs/jboss-websocket-api_1.1_spec-1.1.3.Final.jar:./libs/junit-4.12.jar:./libs/libsigar-amd64-linux.so:./libs/log4j-1.2.17.jar:./libs/mchange-commons-java-0.2.11.jar:./libs/mina-core-2.0.16.jar:./libs/mysql-connector-java-5.1.47.jar:./libs/ojdbc6-11.2.0.4.0.jar:./libs/poi-3.17.jar:./libs/sigar-1.6.4.jar:./libs/sigar-amd64-winnt.dll:./libs/slf4j-api-1.7.21.jar:./libs/slf4j-log4j12-1.7.21.jar:./libs/spring-boot-starter-undertow-2.0.5.RELEASE.jar:./libs/undertow-core-1.4.25.Final.jar:./libs/undertow-servlet-1.4.25.Final.jar:./libs/undertow-websockets-jsr-1.4.25.Final.jar:./libs/xnio-api-3.3.8.Final.jar:./libs/xnio-nio-3.3.8.Final.jar:-server -Djava.ext.dirs="$JAVA_HOME/jre/lib/ext" -Djava.library.path=./libs com.maintain.agent.MaintainAgentMain $1,./deploy/icepool.conf