//isUse表示假死干预机制是否启用
//restartTimeMin判断一次的时间间隔（分钟）
//key是需要干预的程序名
//workDir是程序的工作目录
//deadTimeMin是判断日志最近更新时间距离现在的时间，如果超过配置的时间日志都没有更新，就判定为假死，单位是分钟
//judgeValue是日志中判断连续出现多次就认定假死的字符串，如果为空就只判断日志最后更新时间
//times是认定假死的判断次数

{
  "isUse": false,
  "restartTimeMin": 30,
  "processes": [
    {
      "key": "D:\\dist\\ParseData",
      "workDir": "D:\\dist\\ParseData",
      "deadTimeMin": 30,
      "judgeValue": "",
      "times": 10
    },
    {
      "key": "D:\\dist\\ParseEml",
      "workDir": "D:\\dist\\ParseEml",
      "deadTimeMin": 30,
      "judgeValue": "",
      "times": 10
    }
  ]
}