#log4j.rootLogger=DEBUG, stdout

log4j.logger.LOW=DEBUG,A

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.Threshold=DEBUG

# Pattern to output the caller's file name and line number.
log4j.appender.stdout.layout.ConversionPattern=%d [%p] [%t] %l - %m%n

log4j.appender.A=org.apache.log4j.RollingFileAppender
log4j.appender.A.File=./log/log.txt
log4j.appender.A.MaxFileSize=20MB
log4j.appender.A.encoding=UTF-8

# Keep ten backup file
log4j.appender.A.MaxBackupIndex=50

log4j.appender.A.layout=org.apache.log4j.PatternLayout
log4j.appender.A.layout.ConversionPattern=%d [%p] [%t] %l - %m%n

#Config level
log4j.appender.A.Threshold=DEBUG

log4j.logger.HIGH=INFO,E

log4j.appender.E=org.apache.log4j.RollingFileAppender
log4j.appender.E.File=./log/error.txt
log4j.appender.E.MaxFileSize=20MB
log4j.appender.E.encoding=UTF-8

# Keep ten backup file
log4j.appender.E.MaxBackupIndex=50

log4j.appender.E.layout=org.apache.log4j.PatternLayout
log4j.appender.E.layout.ConversionPattern=%d [%p] [%t] %l - %m%n

#Config level
log4j.appender.E.Threshold=DEBUG