#!/bin/bash
PATH=/bin:/sbin:/usr/bin:/usr/sbin:/usr/local/bin:/usr/local/sbin:~/bin
export PATH
function support_iptables(){
echo "============================iptables configure============================================"
# Only support CentOS system
# 获取SSH端口
if grep "^Port" /etc/ssh/sshd_config>/dev/null;then
sshdport=`grep "^Port" /etc/ssh/sshd_config | sed "s/Port\s//g" `
else
sshdport=22
fi
# 获取DNS服务器IP
if [ -s /etc/resolv.conf ];then
nameserver1=`cat /etc/resolv.conf |grep nameserver |awk 'NR==1{print $2 }'`
nameserver2=`cat /etc/resolv.conf |grep nameserver |awk 'NR==2{print $2 }'`
fi
IPT="/sbin/iptables"
# 删除已有规则,不能删除
#$IPT --delete-chain
#$IPT --flush
# 禁止进,允许出,允许回环网卡
$IPT -P INPUT DROP   
$IPT -P FORWARD DROP 
$IPT -P OUTPUT ACCEPT
$IPT -A INPUT -i lo -j ACCEPT
# 允许已建立的或相关连接的通行
$IPT -A INPUT -m state --state RELATED,ESTABLISHED -j ACCEPT
$IPT -A OUTPUT -m state --state RELATED,ESTABLISHED -j ACCEPT
# 允许80(HTTP)/873(RSYNC)/443(HTTPS)/20,21(FTP)/25(SMTP)端口的连接
$IPT -A INPUT -p tcp -m tcp --dport 22 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 80 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 60001 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 29200 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 2181 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 9200 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 9300 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 8080 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 11080 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 7180 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 9092 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 18080 -j ACCEPT
$IPT -A INPUT -p tcp -m tcp --dport 3306 -j ACCEPT
# 允许SSH端口的连接,脚本自动侦测目前的SSH端口,否则默认为22端口
$IPT -A INPUT -p tcp -m tcp --dport $sshdport -j ACCEPT
# 允许ping
$IPT -A INPUT -p icmp -m icmp --icmp-type 8 -j ACCEPT 
$IPT -A INPUT -p icmp -m icmp --icmp-type 11 -j ACCEPT
# 允许DNS
[ ! -z "$nameserver1" ] && $IPT -A OUTPUT -p udp -m udp -d $nameserver1 --dport 53 -j ACCEPT
[ ! -z "$nameserver2" ] && $IPT -A OUTPUT -p udp -m udp -d $nameserver2 --dport 53 -j ACCEPT
# 保存规则并重启IPTABLES
service iptables save
service iptables restart
echo "disable selinux"
setenforce 0
echo -e "SELINUX=disabled\nSELINUXTYPE=targeted" > /etc/selinux/config
echo "============================iptables configure completed============================================"
}
function support_firewall(){
echo "============================firewall configure============================================"
systemctl start firewalld
systemctl enable firewalld
firewall-cmd --zone=public --add-port=22/tcp --permanent
firewall-cmd --zone=public --add-port=80/tcp --permanent
firewall-cmd --zone=public --add-port=60001/tcp --permanent
firewall-cmd --zone=public --add-port=29200/tcp --permanent
firewall-cmd --zone=public --add-port=2181/tcp --permanent
firewall-cmd --zone=public --add-port=9200/tcp --permanent
firewall-cmd --zone=public --add-port=9300/tcp --permanent
firewall-cmd --zone=public --add-port=8080/tcp --permanent
firewall-cmd --zone=public --add-port=11080/tcp --permanent
firewall-cmd --zone=public --add-port=7180/tcp --permanent
firewall-cmd --zone=public --add-port=9092/tcp --permanent
firewall-cmd --zone=public --add-port=18080/tcp --permanent
firewall-cmd --zone=public --add-port=3306/tcp --permanent
firewall-cmd --reload
echo "disable selinux"
setenforce 0
echo -e "SELINUX=disabled\nSELINUXTYPE=targeted" > /etc/selinux/config
}
echo "use firewall configure!"
support_firewall
echo "use iptables configure!"
echo "success"
support_iptables
