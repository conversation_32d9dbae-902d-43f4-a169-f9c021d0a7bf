{
  "autoNtp": 1,
  //Agent端程序的工作目录名
  "workDirName": "/maintain/MaintainAgent",
  "maintainServer": "MaintainServer:tcp -h 127.0.0.1 -p 60000",
  //运维Server端的地址
  "maintainServerIp": "127.0.0.1",
  //ntp服务器地址
  "ntpServer": "127.0.0.1",
  //运维Agent端的地址
  "maintainAgent": "MaintainAgent:tcp -h ************ -p 60001",
  //向Server端发送心跳的间隔（单位：毫秒）
  "heartbeatInterval": 1,
  //更新内部缓存的间隔（单位：秒）
  "updateCacheInterval": 900,
  //发送监控数据的时间间隔（秒）
  "sendMonitorTime": 30,
  //需要过滤的系统程序名
  "sysProcNames": [
    "rpcbind",
    "rpc.statd",
    "pickup",
    "sshd",
    "auditd",
    "login",
    "rsyslogd",
    "master",
    "qmgr",
    "rpc.idmapd",
    "mingetty",
    "bash",
    "udevd",
    "crond",
    "rpc.mountd",
    "vsftpd",
    "vmware-vmx",
    "DynamicMgr",
    "explorer",
    "services",
    "svchost",
    "wininit"
  ],
  //运维系统监控的程序名
  "userProcNames": [
    "BaoJing",
    "ConfigService",
    "DBCommon",
    "EmlRiskAnalysis",
    "FileThreatScan",
    "HdfsBridge",
    "HlpThreatScan",
    "IpDnsDataMining",
    "IpDnsIndexCreate",
    "LogJudger",
    "MasterIndexServer",
    "MasterSearchServer",
    "OfficeThreatScan",
    "ParseData",
    "ParseEml",
    "PeThreatScan",
    "RelateAnalyse",
    "RiskTransfer",
    "StaticScanCenter",
    "StatisService",
    "SystemReport",
    "TracerFileScheduler",
    "TransitionDataFile",
    "UploadAnaly",
    "XssCheck"
  ],
  //ftp的url前缀
  "ftpPrefix": "ftp://",
  //ftp的文件目录
  "ftpDir": "D:\\ftp",
  //ftp的端口
  "ftpPort": 60003,
  //日志文件预览的字节数
  "browseBytes": 10240,
  //agent生成/读取主机硬件监控的文件路径
  "hardwareMonitoringDirWindow": "D:/dist/monitoring/hardware/",
  "hardwareMonitoringDirLinux": "/home/<USER>/hardware/",
  //agent读取软件系统监控的文件路径
  "softwareMonitoringDirWindow": "D:/dist/monitoring/software/",
  "softwareMonitoringDirLinux": "/home/<USER>/software/",
  "validateKeys": ["java","jdk",".py","./","codis"],
  "monitorTime": 600000
}