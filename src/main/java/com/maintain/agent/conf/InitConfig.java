package com.maintain.agent.conf;

import com.maintain.agent.utils.ServerRequestUtil;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/12/24
 **/
@Configuration
public class InitConfig implements ApplicationContextAware {
    @Value("${maintain.server.url}")
    private String maintainServerUrl;


    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        ServerRequestUtil.maintainServerUrl = maintainServerUrl;
    }
}
