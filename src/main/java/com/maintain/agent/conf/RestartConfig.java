package com.maintain.agent.conf;

import com.maintain.agent.entity.struct.RestartStruct;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2018-02-28
 */
public class RestartConfig {
    /**
     * 需要干预的程序配置
     */
    private List<RestartStruct> processes;
    /**
     * 假死干预机制是否启用
     */
    private boolean isUse;
    /**
     * 判断一次的时间间隔（分钟）
     */
    private int restartTimeMin;

    /**
     * Getter method for property <tt>processes</tt>.
     *
     * @return property value of processes
     */

    public List<RestartStruct> getProcesses() {
        return processes;
    }

    /**
     * Setter method for property <tt>processes</tt>.
     *
     * @param processes value to be assigned to property processes
     */
    public void setProcesses(List<RestartStruct> processes) {
        this.processes = processes;
    }

    /**
     * Getter method for property <tt>isUse</tt>.
     *
     * @return property value of isUse
     */

    public boolean isUse() {
        return isUse;
    }

    /**
     * Setter method for property <tt>isUse</tt>.
     *
     * @param isUse value to be assigned to property isUse
     */
    public void setIsUse(boolean isUse) {
        this.isUse = isUse;
    }

    /**
     * Getter method for property <tt>restartTimeMin</tt>.
     *
     * @return property value of restartTimeMin
     */

    public int getRestartTimeMin() {
        return restartTimeMin;
    }

    /**
     * Setter method for property <tt>restartTimeMin</tt>.
     *
     * @param restartTimeMin value to be assigned to property restartTimeMin
     */
    public void setRestartTimeMin(int restartTimeMin) {
        this.restartTimeMin = restartTimeMin;
    }
}
