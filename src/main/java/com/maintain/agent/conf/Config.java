package com.maintain.agent.conf;

import com.maintain.agent.entity.config.SystemConfig;
import com.common.log.Log;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2016/10/31
 * 本地配置文件读取
 */
public class Config {

    private Config() {
    }

    /**
     * 系统配置（config.json）
     */
    private static SystemConfig systemConfig = null;

    /**
     * 假死干预机制的相关配置
     */
    private static RestartConfig restartConfig = null;
    /**
     * ObjectMapper实例
     */
    private static ObjectMapper objectMapper = null;

    /**
     * Getter method for property <tt>systemConfig</tt>.
     *
     * @return property value of systemConfig
     */

    public static SystemConfig getSystemConfig() {
        return systemConfig;
    }

    /**
     * Setter method for property <tt>systemConfig</tt>.
     *
     * @param systemConfig value to be assigned to property systemConfig
     */
    public static void setSystemConfig(SystemConfig systemConfig) {
        Config.systemConfig = systemConfig;
    }

    /**
     * Getter method for property <tt>objectMapper</tt>.
     *
     * @return property value of objectMapper
     */

    public static ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    /**
     * Setter method for property <tt>objectMapper</tt>.
     *
     * @param objectMapper value to be assigned to property objectMapper
     */
    public static void setObjectMapper(ObjectMapper objectMapper) {
        Config.objectMapper = objectMapper;
    }

    /**
     * Getter method for property <tt>restartConfig</tt>.
     *
     * @return property value of restartConfig
     */

    public static RestartConfig getRestartConfig() {
        return restartConfig;
    }

    /**
     * Setter method for property <tt>restartConfig</tt>.
     *
     * @param restartConfig value to be assigned to property restartConfig
     */
    public static void setRestartConfig(RestartConfig restartConfig) {
        Config.restartConfig = restartConfig;
    }

    static {
        JsonFactory factory = new JsonFactory();
        factory.enable(JsonParser.Feature.ALLOW_COMMENTS);
        objectMapper = new ObjectMapper(factory);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        try {
            File file = new File("./conf/config.json");
            systemConfig = objectMapper.readValue(file, SystemConfig.class);
        } catch (IOException e) {
            Log.high.error("Config init systemConfig readValue error", e);
        }
        if (systemConfig == null) {
            Log.high.error("Config init systemConfig error");
        }

        try {
            File file = new File("./conf/restart.json");
            restartConfig = objectMapper.readValue(file, RestartConfig.class);
        } catch (IOException e) {
            Log.high.error("Config init restartConfig readValue error", e);
        }
        if (restartConfig == null) {
            Log.high.error("Config init restartConfig error");
        }
    }

    /**
     * 检查系统配置
     *
     * @return 返回检查结果
     */
    public static boolean check() {
        if (objectMapper == null) {
            return false;
        }
        if (systemConfig == null || !systemConfig.isValid()) {
            return false;
        }
        if (restartConfig == null) {
            return false;
        }
        Log.low.info("Config.check succeed");
        return true;
    }
}
