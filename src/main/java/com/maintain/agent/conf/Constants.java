package com.maintain.agent.conf;

import java.text.DecimalFormat;

/**
 * 系统常量定义类
 * 包含以下类型的常量：
 * 1. 系统状态码和消息
 * 2. 接口标识符
 * 3. 时间相关常量
 * 4. 文件大小单位
 * 5. 日期时间格式
 * 6. 字符编码
 * 7. 操作系统类型
 * 8. 命令执行相关常量
 * 
 * <AUTHOR>
 * @date 2016/10/31
 */
public class Constants {
    public static final Long SLEEP_TIME = 1000L;

    public static final boolean WINDOWS = false;

    private Constants() {
    }


    public static final String GBK = "GBK";
    /**
     * 常量————参数为空
     */
    public static final String PARAMS_EMPTY = "参数为空";
    /**
     * 常量————参数错误
     */
    public static final String PARAMS_ERROR = "参数错误";
    /**
     * 常量————参数不合法
     */
    public static final String PARAMS_ILLEGAL = "参数不合法";
    /**
     * 常量————拒绝访问
     */
    public static final String ACCESS_DENIED = "拒绝访问";
    /**
     * 常量————未知请求
     */
    public static final String UNKNOWN_REQUEST = "未知请求";
    /**
     * 常量————无可用的服务
     */
    public static final String NO_SERVICE_AVAILABLE = "无可用的服务";
    /**
     * 常量————操作失败
     */
    public static final String FAILURE = "操作失败";
    /**
     * 常量————操作成功
     */
    public static final String SUCCESS = "操作成功";

    /**
     * 接口————获取入库目录大小
     */
    public static final int GET_OTHER_STORE_INFO = 20004;

    /**
     * 接口————按主机，获取主机和程序的简要信息
     */
    public static final int GET_PROFILE_WITH_HOST = 20022;
    /**
     * 接口————获取某主机中某程序的日志信息
     */
    public static final int GET_HOST_PROC_LOGS = 20023;
    /**
     * 接口————下载某主机中某程序的某个日志文件
     */
    public static final int DOWNLOAD_HOST_PROC_LOG = 20024;
    /**
     * 接口————浏览某主机中某程序的某个日志文件（最新的部分内容）
     */
    public static final int BROWSE_HOST_PROC_LOG = 20025;
    /**
     * 接口————获取某主机中某程序的配置文件信息
     */
    public static final int GET_HOST_PROC_CONFINGS = 20026;
    /**
     * 接口————获取机器上的某个文件内容
     */
    public static final int READ_HOST_FILE = 20027;
    /**
     * 接口————回写文本到远程主机上下相应文件
     */
    public static final int WRITE_HOST_FILE = 20028;
    /**
     * 接口————返回AGENT上程序的状态信息
     */
    public static final int GET_PROCESSES_INFO = 20029;
    /**
     * 返回程序占用的端口的详细信息
     */
    public static final int GET_PROCESS_PORT_INFO = 20030;
    /**
     * 接口————单台主机执行时间同步指令
     */
    public static final int EXEC_SYNC_TIME_HOST = 20032;
    /**
     * 接口————获取单台主机时间同步结果
     */
    public static final int GET_SYNC_TIME_HOST = 20034;
    /**
     * 接口————获取单台主机的系统信息
     */
    public static final int GET_SYS_INFO_HOST = 20042;
    /**
     * 接口————获取预处理目录磁盘信息
     */
    public static final int GET_PRE_DISK_INFO = 20044;
    /**
     * 接口————更新logstash配置文件
     */
    public static final int UPDATE_LOGSTASH_CONFIG = 20046;
    /**
     * 接口————关闭本服务的AGENT
     */
    public static final int CLOSE_SERVICE_AGENT = 1;

    public static final int CLOSE_WATCHDOG = 2;

    public static final int ADD_INTO_WATCHDOG = 3;

    public static final int PING = 5;

    public static final int PONG = 5;


//=======================运维系统提供的内部接口标识（仅供SERVER-AGENT使用）==================================
    /**
     * 接口————AGENT发出心跳
     */
    public static final int AGENT_SEND_HEARTBEAT = 20093;
    /**
     * 接口————AGENT接收心跳(开启agent)
     */
    public static final int AGENT_ACCEPT_HEARTBEAT = 20094;
    /**
     * 接口————Agent发送监控数据给server
     */
    public static final int AGENT_SEND_HARDWARE_MONITORING_DATA = 3;

    public static final int AGENT_SEND_SOFTWARE_MONITORING_DATA = 4;

//==============================服务调用的返回状态=============================================
    /**
     * 状态————返回成功
     */
    public static final int SUCCESS_RESPONSE_CODE = 0;
    /**
     * 状态————返回失败
     */
    public static final int FAIL_RESPONSE_CODE = 500;

//=============================获取某个主机的进程详细信息==============================================
    /**
     * 获取所有关注的进程
     */
    public static final String GET_ALL_PROCS_TYPE = "1";
    /**
     * 获取icegridnode相关的进程
     */
    public static final String GET_ICE_PROCS_TYPE = "2";

    /**
     * HTML中换行
     */
    public static final String NEW_LINE_IN_HTML = "</br>";
    /**
     * 命令执行超时时间（单位：毫秒）
     */
    public static final long CMD_EXEC_TIMEOUT = 6000;
    /**
     * 操作系统————windows
     */
    public static final String OS_WINDOWS = "windows";
    /**
     * 操作系统————linux
     */
    public static final String OS_LINUX = "linux";

    /**
     * 一秒的毫秒数
     */
    public static final long ONE_SECOND_MILLISECONDS = 1000;
    /**
     * 一分钟的毫秒数
     */
    public static final long ONE_MINUTE_MILLISECONDS = 60 * ONE_SECOND_MILLISECONDS;
    /**
     * 一小时的毫秒数
     */
    public static final long ONE_HOUR_MILLISECONDS = 60 * ONE_MINUTE_MILLISECONDS;
    /**
     * 一天的毫秒数
     */
    public static final long ONE_DAY_MILLISECONDS = 24 * ONE_HOUR_MILLISECONDS;
    /**
     * 一星期的毫秒数
     */
    public static final long ONE_WEEK_MILLISECONDS = 7 * ONE_DAY_MILLISECONDS;

    /**
     * 1KB的字节数
     */
    public static final long KB_SIZE = 1024;
    /**
     * 1MB的字节数
     */
    public static final long MB_SIZE = 1024 * KB_SIZE;
    /**
     * 1GB的字节数
     */
    public static final long GB_SIZE = 1024 * MB_SIZE;
    /**
     * 1TB的字节数
     */
    public static final long TB_SIZE = 1024 * GB_SIZE;
    /**
     * 1PB的字节数
     */
    public static final long PB_SIZE = 1024 * TB_SIZE;

    /**
     * 时间格式：yyyy-MM-dd
     */
    public static final String YYYY_MM_DD = "yyyy-MM-dd";

    /**
     * 时间格式：yyyy-MM-dd HH:mm:ss
     */
    public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    /**
     * 时间格式：yyyy-MM-dd HH:mm:ss.SSS
     */
    public static final String YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";

    public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    /**
     * 编码（UTF-8）
     */
    public static final String ENCODE_UTF8 = "UTF-8";
    /**
     * 编码（GBK）
     */
    public static final String ENCODE_GBK = "GBK";

    /**
     * cmd命令进程执行结果输出类型：错误信息（error）
     */
    public static final String PROC_RESULT_OUT_TYPE_ERROR = "error";
    /**
     * cmd命令进程执行结果输出类型：输出信息（out）
     */
    public static final String PROC_RESULT_OUT_TYPE_OUT = "out";

    /**
     * 工具常量：截取浮点数至小数点后两位
     */
    public static final DecimalFormat DF = new DecimalFormat("#.00");

    /**
     * NTP服务名
     */
    public static final String NTP = "NTP";

    /**
     * 程序备份
     */
    public static final int BACKUP_PROGRAM = 6;


    /**
     * 业务监控
     */
    public static final int BUSINESS_MONITOR = 7;

    /**
     * 调用python脚本
     */
    public static final int MODIFY_CONFIG = 8;


    /**
     * 0关闭 1启动 2重启程序
     */
    public static final int SOFTWARE_OPERATE = 9;

    public static final int START_LOGSTASH = 10;

    public static final int CONFIRM_LOGSTASH = 11;

    public static final int OPERATE_FIREWALL = 12;

    public static final int PID_FILE = 13;

    public static final int PORT_LIST = 14;

    public static final int EXEC_COMMAND = 15;

    /**
     * 获取程序信息
     */
    public static final int GET_SOFTWARE_INFO = 20040;

    /**
     * 发送程序信息给server
     */
    public static final int SENT_SOFTWARE_INFO = 20041;

    /**
     * 更新程序信息
     */
    public static final int UPDATE_SOFTWARE_INFO = 20043;

    /**
     * 清空重启次数
     */
    public static final int CLEAR_RESTART_COUNT = 20045;

    /**
     * 插入程序重启日志
     */
    public static final int RESTART_LOG = 20047;

    /** 设置堆大小 */
    public static final int SET_HEAP_SIZE = 20050;

    /** 获取程序目录列表 */
    public static final int GET_SOFTWARE_LIST = 20052;

    /** 一键时间同步*/
    public static final int SET_SYSCHRONISED_TIME = 20099;

    /** Ping所有的Agent */
    public static final int PONG_ALL_AGENT = 20100;
    /** 检测数据库备份情况 */
    public static final int CHECK_DB_BACKUP = 20101;

    /** 获取es目录*/
    public static final int GET_ESLOG_PATH = 20054;

    /**获取时间戳 */
    public static final int TIME = 20066;

    public static final int DELETE_OLD_SOFTWARE = 20067;

    public final static String LOCAL_IP = "127.0.0.1";

    public final static int REPLACE_CONFIG = 30000;

    public final static int GET_FILE_SIZE = 30001;

    public final static int PING_AGENT = 30002;


}
