package com.maintain.agent.entity.struct;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2016/10/27
 */
public class HostProcsStruct {
    /**
     * ip
     */
    private String host;
    /**
     * 进程列表
     */
    private List<String> procs;

    /**
     * Getter method for property <tt>host</tt>.
     *
     * @return property value of host
     */

    public String getHost() {
        return host;
    }

    /**
     * Setter method for property <tt>host</tt>.
     *
     * @param host value to be assigned to property host
     */
    public void setHost(String host) {
        this.host = host;
    }

    /**
     * Getter method for property <tt>procs</tt>.
     *
     * @return property value of procs
     */

    public List<String> getProcs() {
        return procs;
    }

    /**
     * Setter method for property <tt>procs</tt>.
     *
     * @param procs value to be assigned to property procs
     */
    public void setProcs(List<String> procs) {
        this.procs = procs;
    }
}
