package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2016/11/6
 * ProcStruct的原始形式，用于缓存进程的多个属性
 */
public class ProcStructRaw {
    /**
     * 进程PID
     */
    private String pid;
    /**
     * 进程名称
     */
    private String name;
    /**
     * 进程父进程
     */
    private String pp;
    /**
     * 进程启动时间
     */
    private String time;
    /**
     * 进程启动命令
     */
    private String cmd;
    /**
     * 进程工作目录
     */
    private String cwd;

    /**
     * Getter method for property <tt>pid</tt>.
     *
     * @return property value of pid
     */

    public String getPid() {
        return pid;
    }

    /**
     * Setter method for property <tt>pid</tt>.
     *
     * @param pid value to be assigned to property pid
     */
    public void setPid(String pid) {
        this.pid = pid;
    }

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter method for property <tt>pp</tt>.
     *
     * @return property value of pp
     */

    public String getPp() {
        return pp;
    }

    /**
     * Setter method for property <tt>pp</tt>.
     *
     * @param pp value to be assigned to property pp
     */
    public void setPp(String pp) {
        this.pp = pp;
    }

    /**
     * Getter method for property <tt>time</tt>.
     *
     * @return property value of time
     */

    public String getTime() {
        return time;
    }

    /**
     * Setter method for property <tt>time</tt>.
     *
     * @param time value to be assigned to property time
     */
    public void setTime(String time) {
        this.time = time;
    }

    /**
     * Getter method for property <tt>cmd</tt>.
     *
     * @return property value of cmd
     */

    public String getCmd() {
        return cmd;
    }

    /**
     * Setter method for property <tt>cmd</tt>.
     *
     * @param cmd value to be assigned to property cmd
     */
    public void setCmd(String cmd) {
        this.cmd = cmd;
    }

    /**
     * Getter method for property <tt>cwd</tt>.
     *
     * @return property value of cwd
     */

    public String getCwd() {
        return cwd;
    }

    /**
     * Setter method for property <tt>cwd</tt>.
     *
     * @param cwd value to be assigned to property cwd
     */
    public void setCwd(String cwd) {
        this.cwd = cwd;
    }
}
