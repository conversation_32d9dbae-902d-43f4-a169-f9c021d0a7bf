package com.maintain.agent.entity.struct;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018-11-26
 */
public class BackupStruct {

    /**
     * 程序-端口
     */
    private Map<String,String> programMap;

    private String name;

    private String remotePath;

    public Map<String, String> getProgramMap() {
        return programMap;
    }

    /**
     * Setter method for property <tt>programMap</tt>.
     *
     * @param programMap value to be assigned to property programMap
     */
    public void setProgramMap(Map<String, String> programMap) {
        this.programMap = programMap;
    }

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    public String getRemotePath() {
        return remotePath;
    }

    /**
     * Setter method for property <tt>remotePath</tt>.
     *
     * @param remotePath value to be assigned to property remotePath
     */
    public void setRemotePath(String remotePath) {
        this.remotePath = remotePath;
    }
}