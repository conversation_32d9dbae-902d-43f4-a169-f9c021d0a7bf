package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2016/11/23
 */
public class SyncTimeStruct {
    /**
     * ip
     */
    private String host;
    /**
     * 时间
     */
    private String time;

    private SyncTimeStruct() {
    }

    /**
     * 构造此类的实例
     *
     * @param host ip
     * @param time 时间
     * @return 返回此类的实例
     */
    public static SyncTimeStruct valueOf(String host, String time) {
        SyncTimeStruct struct = new SyncTimeStruct();
        struct.setHost(host);
        struct.setTime(time);
        return struct;
    }

    /**
     * Getter method for property <tt>host</tt>.
     *
     * @return property value of host
     */

    public String getHost() {
        return host;
    }

    /**
     * Setter method for property <tt>host</tt>.
     *
     * @param host value to be assigned to property host
     */
    public void setHost(String host) {
        this.host = host;
    }

    /**
     * Getter method for property <tt>time</tt>.
     *
     * @return property value of time
     */

    public String getTime() {
        return time;
    }

    /**
     * Setter method for property <tt>time</tt>.
     *
     * @param time value to be assigned to property time
     */
    public void setTime(String time) {
        this.time = time;
    }
}
