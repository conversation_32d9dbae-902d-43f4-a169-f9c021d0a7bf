package com.maintain.agent.entity.struct;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 * @date 2018-10-10
 */
public class AnsecProcesskeepVo {

    @JsonProperty(value = "exepath")
    private String exePath;

    @JsonProperty(value = "workpath")
    private String workPath;

    @JsonProperty(value = "keepruntime")
    private String keepRuntime;

    @JsonProperty(value = "cpulimit")
    private String cpuLimit;

    @JsonProperty(value = "paremter")
    private String parameter;

    private String hide;

    public String getExePath() {
        return exePath;
    }

    /**
     * Setter method for property <tt>exePath</tt>.
     *
     * @param exePath value to be assigned to property exePath
     */
    public void setExePath(String exePath) {
        this.exePath = exePath;
    }

    public String getWorkPath() {
        return workPath;
    }

    /**
     * Setter method for property <tt>workPath</tt>.
     *
     * @param workPath value to be assigned to property workPath
     */
    public void setWorkPath(String workPath) {
        this.workPath = workPath;
    }

    public String getKeepRuntime() {
        return keepRuntime;
    }

    /**
     * Setter method for property <tt>keepRuntime</tt>.
     *
     * @param keepRuntime value to be assigned to property keepRuntime
     */
    public void setKeepRuntime(String keepRuntime) {
        this.keepRuntime = keepRuntime;
    }

    public String getCpuLimit() {
        return cpuLimit;
    }

    /**
     * Setter method for property <tt>cpuLimit</tt>.
     *
     * @param cpuLimit value to be assigned to property cpuLimit
     */
    public void setCpuLimit(String cpuLimit) {
        this.cpuLimit = cpuLimit;
    }

    public String getParameter() {
        return parameter;
    }

    /**
     * Setter method for property <tt>parameter</tt>.
     *
     * @param parameter value to be assigned to property parameter
     */
    public void setParameter(String parameter) {
        this.parameter = parameter;
    }

    public String getHide() {
        return hide;
    }

    /**
     * Setter method for property <tt>hide</tt>.
     *
     * @param hide value to be assigned to property hide
     */
    public void setHide(String hide) {
        this.hide = hide;
    }
}