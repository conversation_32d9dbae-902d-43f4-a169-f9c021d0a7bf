package com.maintain.agent.entity.struct;

import com.maintain.agent.utils.DataTransUtil;
import org.hyperic.sigar.Mem;

/**
 * <AUTHOR>
 * @date 2016/11/29
 */
public class MemStruct {
    /**
     * 内存总量
     */
    private String total;
    /**
     * 剩余内存空间
     */
    private String free;
    /**
     * 已用内存空间
     */
    private String used;
    /**
     * 内存使用率
     */
    private String usedPercent;
    /**
     * 实际剩余空间
     */
    private String actualFree;
    /**
     * 实际内存使用量
     */
    private String actualUsed;

    private MemStruct() {
    }

    /**
     * 构造此类的实例
     *
     * @param mem Mem实例
     * @return 返回此类的实例
     */
    public static MemStruct valueOf(Mem mem) {
        MemStruct struct = new MemStruct();
        // 总内存
        struct.setTotal(DataTransUtil.toBytesStrFormat(mem.getTotal()));
        struct.setFree(DataTransUtil.toBytesStrFormat(mem.getFree()));
        // 已用内存
        struct.setUsed(DataTransUtil.toBytesStrFormat(mem.getUsed()));
        // 已用内存/总内存 * 100&
        //BigDecimal totalMemory = new BigDecimal(mem.getTotal());
       // BigDecimal usedMemory = new BigDecimal(mem.getUsed());
       // usedMemory = totalMemory.divide(usedMemory,2,BigDecimal.ROUND_CEILING);
        struct.setUsedPercent(DataTransUtil.toPercentStrFormat(mem.getUsedPercent()));
        struct.setActualFree(DataTransUtil.toBytesStrFormat(mem.getActualFree()));
        struct.setActualUsed(DataTransUtil.toBytesStrFormat(mem.getActualUsed()));
        return struct;
    }

    /**
     * Getter method for property <tt>total</tt>.
     *
     * @return property value of total
     */

    public String getTotal() {
        return total;
    }

    /**
     * Setter method for property <tt>total</tt>.
     *
     * @param total value to be assigned to property total
     */
    public void setTotal(String total) {
        this.total = total;
    }

    /**
     * Getter method for property <tt>free</tt>.
     *
     * @return property value of free
     */

    public String getFree() {
        return free;
    }

    /**
     * Setter method for property <tt>free</tt>.
     *
     * @param free value to be assigned to property free
     */
    public void setFree(String free) {
        this.free = free;
    }

    /**
     * Getter method for property <tt>used</tt>.
     *
     * @return property value of used
     */

    public String getUsed() {
        return used;
    }

    /**
     * Setter method for property <tt>used</tt>.
     *
     * @param used value to be assigned to property used
     */
    public void setUsed(String used) {
        this.used = used;
    }

    /**
     * Getter method for property <tt>usedPercent</tt>.
     *
     * @return property value of usedPercent
     */

    public String getUsedPercent() {
        return usedPercent;
    }

    /**
     * Setter method for property <tt>usedPercent</tt>.
     *
     * @param usedPercent value to be assigned to property usedPercent
     */
    public void setUsedPercent(String usedPercent) {
        this.usedPercent = usedPercent;
    }

    /**
     * Getter method for property <tt>actualFree</tt>.
     *
     * @return property value of actualFree
     */

    public String getActualFree() {
        return actualFree;
    }

    /**
     * Setter method for property <tt>actualFree</tt>.
     *
     * @param actualFree value to be assigned to property actualFree
     */
    public void setActualFree(String actualFree) {
        this.actualFree = actualFree;
    }

    /**
     * Getter method for property <tt>actualUsed</tt>.
     *
     * @return property value of actualUsed
     */

    public String getActualUsed() {
        return actualUsed;
    }

    /**
     * Setter method for property <tt>actualUsed</tt>.
     *
     * @param actualUsed value to be assigned to property actualUsed
     */
    public void setActualUsed(String actualUsed) {
        this.actualUsed = actualUsed;
    }
}
