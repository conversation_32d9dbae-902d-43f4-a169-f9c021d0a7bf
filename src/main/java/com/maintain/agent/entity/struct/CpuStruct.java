package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2016/11/29
 */
public class CpuStruct {
    /**
     * 型号
     */
    private String type;
    /**
     * 总核心数
     */
    private String totalCore;
    /**
     * cpu使用百分比
     */
    private String usedPercent;
    /**
     * 供应商
     */
    private String vendor;
    /**
     * 主频
     */
    private String mhz;

    /**
     * Getter method for property <tt>type</tt>.
     *
     * @return property value of type
     */

    public String getType() {
        return type;
    }

    /**
     * Setter method for property <tt>type</tt>.
     *
     * @param type value to be assigned to property type
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * Getter method for property <tt>totalCore</tt>.
     *
     * @return property value of totalCore
     */

    public String getTotalCore() {
        return totalCore;
    }

    /**
     * Setter method for property <tt>totalCore</tt>.
     *
     * @param totalCore value to be assigned to property totalCore
     */
    public void setTotalCore(String totalCore) {
        this.totalCore = totalCore;
    }

    /**
     * Getter method for property <tt>usedPercent</tt>.
     *
     * @return property value of usedPercent
     */

    public String getUsedPercent() {
        return usedPercent;
    }

    /**
     * Setter method for property <tt>usedPercent</tt>.
     *
     * @param usedPercent value to be assigned to property usedPercent
     */
    public void setUsedPercent(String usedPercent) {
        this.usedPercent = usedPercent;
    }

    /**
     * Getter method for property <tt>vendor</tt>.
     *
     * @return property value of vendor
     */

    public String getVendor() {
        return vendor;
    }

    /**
     * Setter method for property <tt>vendor</tt>.
     *
     * @param vendor value to be assigned to property vendor
     */
    public void setVendor(String vendor) {
        this.vendor = vendor;
    }

    /**
     * Getter method for property <tt>mhz</tt>.
     *
     * @return property value of mhz
     */

    public String getMhz() {
        return mhz;
    }

    /**
     * Setter method for property <tt>mhz</tt>.
     *
     * @param mhz value to be assigned to property mhz
     */
    public void setMhz(String mhz) {
        this.mhz = mhz;
    }
}
