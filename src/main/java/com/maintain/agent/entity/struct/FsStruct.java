package com.maintain.agent.entity.struct;

import com.maintain.agent.utils.DataTransUtil;

/**
 * <AUTHOR>
 * @date 2016/11/29
 */
public class FsStruct {
    /**
     * 盘符
     */
    private String name;
    /**
     * 总空间
     */
    private String total;
    /**
     * 可用空间
     */
    private String avail;
    /**
     * 已用空间
     */
    private String used;
    /**
     * 使用率
     */
    private String usedPercent;

    public FsStruct() {
    }

    /**
     * 构造此类的实例
     *
     * @param info FileSystemInfoStruct实例
     * @return 返回此类的实例
     */
    public static FsStruct valueOf(FileSystemInfoStruct info) {
        FsStruct struct = new FsStruct();
        struct.setName(info.getFileSystem());
        struct.setTotal(DataTransUtil.toBytesStrFormat(info.getTotal()));
        struct.setAvail(DataTransUtil.toBytesStrFormat(info.getAvail()));
        struct.setUsed(DataTransUtil.toBytesStrFormat(info.getUsed()));
        struct.setUsedPercent(info.getUsedPercent() + "%");
        return struct;
    }


    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter method for property <tt>total</tt>.
     *
     * @return property value of total
     */

    public String getTotal() {
        return total;
    }

    /**
     * Setter method for property <tt>total</tt>.
     *
     * @param total value to be assigned to property total
     */
    public void setTotal(String total) {
        this.total = total;
    }

    /**
     * Getter method for property <tt>avail</tt>.
     *
     * @return property value of avail
     */

    public String getAvail() {
        return avail;
    }

    /**
     * Setter method for property <tt>avail</tt>.
     *
     * @param avail value to be assigned to property avail
     */
    public void setAvail(String avail) {
        this.avail = avail;
    }

    /**
     * Getter method for property <tt>used</tt>.
     *
     * @return property value of used
     */

    public String getUsed() {
        return used;
    }

    /**
     * Setter method for property <tt>used</tt>.
     *
     * @param used value to be assigned to property used
     */
    public void setUsed(String used) {
        this.used = used;
    }

    /**
     * Getter method for property <tt>usedPercent</tt>.
     *
     * @return property value of usedPercent
     */

    public String getUsedPercent() {
        return usedPercent;
    }

    /**
     * Setter method for property <tt>usedPercent</tt>.
     *
     * @param usedPercent value to be assigned to property usedPercent
     */
    public void setUsedPercent(String usedPercent) {
        this.usedPercent = usedPercent;
    }
}
