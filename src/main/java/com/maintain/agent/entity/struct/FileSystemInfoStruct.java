package com.maintain.agent.entity.struct;

import com.maintain.agent.conf.Constants;

/**
 * <AUTHOR>
 * @date 2018-01-31
 */
public class FileSystemInfoStruct {
    /**
     * 文件系统，如：C:\
     */
    private String fileSystem;
    /**
     * 总空间（字节）
     */
    private long total;
    /**
     * 剩余空间（字节）
     */
    private long avail;
    /**
     * 使用空间（字节）
     */
    private long used;
    /**
     * 使用率(去掉了百分号)
     */
    private double usedPercent;

    /**
     * 监控预处理目录大小
     */
    private long  dirSize;

    public FileSystemInfoStruct(String fileSystem, long total, long avail, long used) {
        this.fileSystem = fileSystem;
        this.total = total;
        this.avail = avail;
        this.used = used;
        double per = used * 100.0 / total;
        this.usedPercent = Double.parseDouble(Constants.DF.format(per));
    }

    public FileSystemInfoStruct(String fileSystem, long total, long avail, long used, long dirSize) {
        this.fileSystem = fileSystem;
        this.total = total;
        this.avail = avail;
        this.used = used;
        double per = used * 100.0 / total;
        this.usedPercent = Double.parseDouble(Constants.DF.format(per));
        this.dirSize = dirSize;
    }

    /**
     * Getter method for property <tt>fileSystem</tt>.
     *
     * @return property value of fileSystem
     */

    public String getFileSystem() {
        return fileSystem;
    }

    /**
     * Setter method for property <tt>fileSystem</tt>.
     *
     * @param fileSystem value to be assigned to property fileSystem
     */
    public void setFileSystem(String fileSystem) {
        this.fileSystem = fileSystem;
    }

    /**
     * Getter method for property <tt>total</tt>.
     *
     * @return property value of total
     */

    public long getTotal() {
        return total;
    }

    /**
     * Setter method for property <tt>total</tt>.
     *
     * @param total value to be assigned to property total
     */
    public void setTotal(long total) {
        this.total = total;
    }

    /**
     * Getter method for property <tt>avail</tt>.
     *
     * @return property value of avail
     */

    public long getAvail() {
        return avail;
    }

    /**
     * Setter method for property <tt>avail</tt>.
     *
     * @param avail value to be assigned to property avail
     */
    public void setAvail(long avail) {
        this.avail = avail;
    }

    /**
     * Getter method for property <tt>used</tt>.
     *
     * @return property value of used
     */

    public long getUsed() {
        return used;
    }

    /**
     * Setter method for property <tt>used</tt>.
     *
     * @param used value to be assigned to property used
     */
    public void setUsed(long used) {
        this.used = used;
    }

    /**
     * Getter method for property <tt>usedPercent</tt>.
     *
     * @return property value of usedPercent
     */

    public double getUsedPercent() {
        return usedPercent;
    }

    /**
     * Setter method for property <tt>usedPercent</tt>.
     *
     * @param usedPercent value to be assigned to property usedPercent
     */
    public void setUsedPercent(double usedPercent) {
        this.usedPercent = usedPercent;
    }

    public long getDirSize() {
        return dirSize;
    }

    public void setDirSize(long dirSize) {
        this.dirSize = dirSize;
    }
}
