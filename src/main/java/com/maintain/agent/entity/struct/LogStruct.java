package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2016/10/27
 */
public class LogStruct {
    /**
     * 文件名
     */
    private String name;
    /**
     * 最后更新时间
     */
    private String time;
    /**
     * 文件大小
     */
    private String size;

    /**
     * Getter method for property <tt>name</tt>.
     *
     * @return property value of name
     */

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * Getter method for property <tt>time</tt>.
     *
     * @return property value of time
     */

    public String getTime() {
        return time;
    }

    /**
     * Setter method for property <tt>time</tt>.
     *
     * @param time value to be assigned to property time
     */
    public void setTime(String time) {
        this.time = time;
    }

    /**
     * Getter method for property <tt>size</tt>.
     *
     * @return property value of size
     */

    public String getSize() {
        return size;
    }

    /**
     * Setter method for property <tt>size</tt>.
     *
     * @param size value to be assigned to property size
     */
    public void setSize(String size) {
        this.size = size;
    }
}
