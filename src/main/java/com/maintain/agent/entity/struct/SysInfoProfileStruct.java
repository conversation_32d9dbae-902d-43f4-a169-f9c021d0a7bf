package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2016/11/23
 */
public class SysInfoProfileStruct {
    /**
     * ip
     */
    private String host;
    /**
     * 操作系统
     */
    private String os;
    /**
     * 文件系统信息
     */
    private String fs;
    /**
     * 内存信息
     */
    private String mem;
    /**
     * 当前时间
     */
    private String now;

    private SysInfoProfileStruct() {
    }

    /**
     * 构造此类的实例
     *
     * @param host ip
     * @param os   操作系统
     * @param fs   文件系统
     * @param mem  内存信息
     * @param now  当前时间
     * @return 返回此类的实例
     */
    public static SysInfoProfileStruct valueOf(String host, String os, String fs, String mem, String now) {
        SysInfoProfileStruct struct = new SysInfoProfileStruct();
        struct.setHost(host);
        struct.setOs(os);
        struct.setFs(fs);
        struct.setMem(mem);
        struct.setNow(now);
        return struct;
    }

    /**
     * Getter method for property <tt>host</tt>.
     *
     * @return property value of host
     */

    public String getHost() {
        return host;
    }

    /**
     * Setter method for property <tt>host</tt>.
     *
     * @param host value to be assigned to property host
     */
    public void setHost(String host) {
        this.host = host;
    }

    /**
     * Getter method for property <tt>os</tt>.
     *
     * @return property value of os
     */

    public String getOs() {
        return os;
    }

    /**
     * Setter method for property <tt>os</tt>.
     *
     * @param os value to be assigned to property os
     */
    public void setOs(String os) {
        this.os = os;
    }

    /**
     * Getter method for property <tt>fs</tt>.
     *
     * @return property value of fs
     */

    public String getFs() {
        return fs;
    }

    /**
     * Setter method for property <tt>fs</tt>.
     *
     * @param fs value to be assigned to property fs
     */
    public void setFs(String fs) {
        this.fs = fs;
    }

    /**
     * Getter method for property <tt>mem</tt>.
     *
     * @return property value of mem
     */

    public String getMem() {
        return mem;
    }

    /**
     * Setter method for property <tt>mem</tt>.
     *
     * @param mem value to be assigned to property mem
     */
    public void setMem(String mem) {
        this.mem = mem;
    }

    /**
     * Getter method for property <tt>now</tt>.
     *
     * @return property value of now
     */

    public String getNow() {
        return now;
    }

    /**
     * Setter method for property <tt>now</tt>.
     *
     * @param now value to be assigned to property now
     */
    public void setNow(String now) {
        this.now = now;
    }
}
