package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2018-11-30
 */
public class OperateTypeStruct {

    private Integer operateType;

    private String name;

    private Integer pid;

    public Integer getOperateType() {
        return operateType;
    }

    /**
     * Setter method for property <tt>operateType</tt>.
     *
     * @param operateType value to be assigned to property operateType
     */
    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    public Integer getPid() {
        return pid;
    }

    /**
     * Setter method for property <tt>pid</tt>.
     *
     * @param pid value to be assigned to property pid
     */
    public void setPid(Integer pid) {
        this.pid = pid;
    }
}