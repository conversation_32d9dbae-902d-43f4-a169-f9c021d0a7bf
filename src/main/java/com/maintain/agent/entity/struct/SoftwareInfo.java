package com.maintain.agent.entity.struct;

import java.util.Date;

/**
 * 程序信息
 */
public class SoftwareInfo {

    private Integer id;

    /**
     * 程序名
     */
    private String name;

    /**
     * 进程id
     */
    private Integer pid;

    /**
     * 状态  开启：1 ，关闭：0
     */
    private Integer programStatus;

    /**
     * 重启次数
     */
    private Integer restartCount;

    /**
     * 操作类型 手动开启关闭：1，agent守护开启：2
     */
    private Integer operateType;

    /**
     * 最近一次重启时间
     */
    private Date restartTime;

    /**
     * 进程数
     */
    private Integer processCount;

    /**
     * error日志数量
     */
    private Integer logCount;

    /**
     * 启动脚本是否存在
     */
    private Integer isExists;

    /**
     * 所在目录
     */
    private String realDir;

    /**
     * 是否手动配置启动程序
     */
    private Boolean config;

    /**
     * 识别程序的唯一key
     */
    private String keys;

    /**
     * 启动脚本的路径
     */
    private String scriptPath;

    /**
     * 脚本启动命令
     */
    private String script;

    private Integer heartMonitor = 0;

    private String logPath;
    private String logSuffix;
    private String configPath;

    public Integer getMinHeapSize() {
        return minHeapSize;
    }

    public void setMinHeapSize(Integer minHeapSize) {
        this.minHeapSize = minHeapSize;
    }

    public Integer getMaxHeapSize() {
        return maxHeapSize;
    }

    public void setMaxHeapSize(Integer maxHeapSize) {
        this.maxHeapSize = maxHeapSize;
    }

    /**
     * 堆大小 单位为mb
     */
    private Integer minHeapSize;

    private Integer maxHeapSize;

    /**
     * 关闭命令
     */
    private String closeScript;

    public String getCloseScript() {
        return closeScript;
    }

    public void setCloseScript(String closeScript) {
        this.closeScript = closeScript;
    }

    public Boolean getConfig() {
        return config;
    }

    public void setConfig(Boolean config) {
        this.config = config;
    }

    public String getKeys() {
        return keys;
    }

    public void setKeys(String keys) {
        this.keys = keys;
    }

    public String getScriptPath() {
        return scriptPath;
    }

    public void setScriptPath(String scriptPath) {
        this.scriptPath = scriptPath;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getRealDir() {
        return realDir;
    }

    public void setRealDir(String realDir) {
        this.realDir = realDir;
    }

    public Integer getLogCount() {
        return logCount;
    }

    public void setLogCount(Integer logCount) {
        this.logCount = logCount;
    }

    public Integer getProcessCount() {
        return processCount;
    }

    public void setProcessCount(Integer processCount) {
        this.processCount = processCount;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    public Integer getProgramStatus() {
        return programStatus;
    }

    public void setProgramStatus(Integer programStatus) {
        this.programStatus = programStatus;
    }

    public Integer getRestartCount() {
        return restartCount;
    }

    public void setRestartCount(Integer restartCount) {
        this.restartCount = restartCount;
    }

    public Integer getOperateType() {
        return operateType;
    }

    public void setOperateType(Integer operateType) {
        this.operateType = operateType;
    }

    public Date getRestartTime() {
        return restartTime;
    }

    public void setRestartTime(Date restartTime) {
        this.restartTime = restartTime;
    }

    public Integer getIsExists() {
        return isExists;
    }

    public void setIsExists(Integer isExists) {
        this.isExists = isExists;
    }

    public Integer getHeartMonitor() {
        return heartMonitor;
    }

    public void setHeartMonitor(Integer heartMonitor) {
        this.heartMonitor = heartMonitor;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public String getLogSuffix() {
        return logSuffix;
    }

    public void setLogSuffix(String logSuffix) {
        this.logSuffix = logSuffix;
    }

    public String getConfigPath() {
        return configPath;
    }

    public void setConfigPath(String configPath) {
        this.configPath = configPath;
    }

    @Override
    public String toString() {
        return "SoftwareInfo{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", pid=" + pid +
                ", programStatus=" + programStatus +
                ", restartCount=" + restartCount +
                ", operateType=" + operateType +
                ", restartTime=" + restartTime +
                ", processCount=" + processCount +
                ", logCount=" + logCount +
                ", isExists=" + isExists +
                ", realDir='" + realDir + '\'' +
                ", config=" + config +
                ", keys='" + keys + '\'' +
                ", scriptPath='" + scriptPath + '\'' +
                ", script='" + script + '\'' +
                ", minHeapSize=" + minHeapSize +
                ", maxHeapSize=" + maxHeapSize +
                ", closeScript='" + closeScript + '\'' +
                '}';
    }
}
