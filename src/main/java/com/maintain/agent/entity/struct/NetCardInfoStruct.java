package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2017/12/21
 */
public class NetCardInfoStruct {
    /**
     * NetInterfaceConfig实例
     */
    private NetInterfaceConfigVo netInterfaceConfig;
    /**
     * 网卡带宽
     */
    private String speed;

    /**
     * Getter method for property <tt>netInterfaceConfig</tt>.
     *
     * @return property value of netInterfaceConfig
     */

    public NetInterfaceConfigVo getNetInterfaceConfig() {
        return netInterfaceConfig;
    }

    /**
     * Setter method for property <tt>netInterfaceConfig</tt>.
     *
     * @param netInterfaceConfig value to be assigned to property netInterfaceConfig
     */
    public void setNetInterfaceConfig(NetInterfaceConfigVo netInterfaceConfig) {
        this.netInterfaceConfig = netInterfaceConfig;
    }

    /**
     * Getter method for property <tt>speed</tt>.
     *
     * @return property value of speed
     */

    public String getSpeed() {
        return speed;
    }

    /**
     * Setter method for property <tt>speed</tt>.
     *
     * @param speed value to be assigned to property speed
     */
    public void setSpeed(String speed) {
        this.speed = speed;
    }
}
