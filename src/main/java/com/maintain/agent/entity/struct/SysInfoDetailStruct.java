package com.maintain.agent.entity.struct;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2016/11/23
 */
public class SysInfoDetailStruct {
    /**
     * ip
     */
    private String host;
    /**
     * mac
     */
    private String mac;
    /**
     * 操作系统
     */
    private String os;
    /**
     * 文件系统信息
     */
    private List<FsStruct> fs;
    /**
     * cpu信息
     */
    private CpuStruct cpu;
    /**
     * 内存信息
     */
    private MemStruct mem;
    /**
     * 时间
     */
    private String time;

    private FsStruct diskInfo;

    private SysInfoDetailStruct() {
    }

    /**
     * 构造此类的实例
     *
     * @param host ip
     * @param mac  mac
     * @param os   操作系统
     * @param fs   文件系统
     * @param cpu  cpu信息
     * @param mem  内存信息
     * @param time 时间
     * @return 返回此类的实例
     */
    public static SysInfoDetailStruct valueOf(String host, String mac, String os, List<FsStruct> fs, CpuStruct cpu,
                                              MemStruct mem, String time) {
        SysInfoDetailStruct struct = new SysInfoDetailStruct();
        struct.setHost(host);
        struct.setMac(mac);
        struct.setOs(os);
        struct.setFs(fs);
        struct.setCpu(cpu);
        struct.setMem(mem);
        struct.setTime(time);
        return struct;
    }

    /**
     * Getter method for property <tt>host</tt>.
     *
     * @return property value of host
     */

    public String getHost() {
        return host;
    }

    /**
     * Setter method for property <tt>host</tt>.
     *
     * @param host value to be assigned to property host
     */
    public void setHost(String host) {
        this.host = host;
    }

    /**
     * Getter method for property <tt>mac</tt>.
     *
     * @return property value of mac
     */

    public String getMac() {
        return mac;
    }

    /**
     * Setter method for property <tt>mac</tt>.
     *
     * @param mac value to be assigned to property mac
     */
    public void setMac(String mac) {
        this.mac = mac;
    }

    /**
     * Getter method for property <tt>os</tt>.
     *
     * @return property value of os
     */

    public String getOs() {
        return os;
    }

    /**
     * Setter method for property <tt>os</tt>.
     *
     * @param os value to be assigned to property os
     */
    public void setOs(String os) {
        this.os = os;
    }

    /**
     * Getter method for property <tt>fs</tt>.
     *
     * @return property value of fs
     */

    public List<FsStruct> getFs() {
        return fs;
    }

    /**
     * Setter method for property <tt>fs</tt>.
     *
     * @param fs value to be assigned to property fs
     */
    public void setFs(List<FsStruct> fs) {
        this.fs = fs;
    }

    /**
     * Getter method for property <tt>cpu</tt>.
     *
     * @return property value of cpu
     */

    public CpuStruct getCpu() {
        return cpu;
    }

    /**
     * Setter method for property <tt>cpu</tt>.
     *
     * @param cpu value to be assigned to property cpu
     */
    public void setCpu(CpuStruct cpu) {
        this.cpu = cpu;
    }

    /**
     * Getter method for property <tt>mem</tt>.
     *
     * @return property value of mem
     */

    public MemStruct getMem() {
        return mem;
    }

    /**
     * Setter method for property <tt>mem</tt>.
     *
     * @param mem value to be assigned to property mem
     */
    public void setMem(MemStruct mem) {
        this.mem = mem;
    }

    /**
     * Getter method for property <tt>time</tt>.
     *
     * @return property value of time
     */

    public String getTime() {
        return time;
    }

    /**
     * Setter method for property <tt>time</tt>.
     *
     * @param time value to be assigned to property time
     */
    public void setTime(String time) {
        this.time = time;
    }

    public FsStruct getDiskInfo() {
        return diskInfo;
    }

    /**
     * Setter method for property <tt>diskInfo</tt>.
     *
     * @param diskInfo value to be assigned to property diskInfo
     */
    public void setDiskInfo(FsStruct diskInfo) {
        this.diskInfo = diskInfo;
    }
}
