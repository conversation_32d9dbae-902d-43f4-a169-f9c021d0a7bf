package com.maintain.agent.entity.struct;

/**
 * <AUTHOR>
 * @date 2018-02-28
 */
public class RestartStruct {
    /**
     * 需要干预的程序名
     */
    private String key;
    /**
     * 程序的工作目录
     */
    private String workDir;
    /**
     * 判断日志最近更新时间距离现在的时间，如果超过配置的时间日志都没有更新，就判定为假死，单位是分钟
     */
    private int deadTimeMin;
    /**
     * 日志中判断连续出现多次就认定假死的字符串，如果为空就只判断日志最后更新时间
     */
    private String judgeValue;
    /**
     * 认定假死的判断次数
     */
    private int times;

    /**
     * Getter method for property <tt>key</tt>.
     *
     * @return property value of key
     */

    public String getKey() {
        return key;
    }

    /**
     * Setter method for property <tt>key</tt>.
     *
     * @param key value to be assigned to property key
     */
    public void setKey(String key) {
        this.key = key;
    }

    /**
     * Getter method for property <tt>workDir</tt>.
     *
     * @return property value of workDir
     */

    public String getWorkDir() {
        return workDir;
    }

    /**
     * Setter method for property <tt>workDir</tt>.
     *
     * @param workDir value to be assigned to property workDir
     */
    public void setWorkDir(String workDir) {
        this.workDir = workDir;
    }

    /**
     * Getter method for property <tt>deadTimeMin</tt>.
     *
     * @return property value of deadTimeMin
     */

    public int getDeadTimeMin() {
        return deadTimeMin;
    }

    /**
     * Setter method for property <tt>deadTimeMin</tt>.
     *
     * @param deadTimeMin value to be assigned to property deadTimeMin
     */
    public void setDeadTimeMin(int deadTimeMin) {
        this.deadTimeMin = deadTimeMin;
    }

    /**
     * Getter method for property <tt>judgeValue</tt>.
     *
     * @return property value of judgeValue
     */

    public String getJudgeValue() {
        return judgeValue;
    }

    /**
     * Setter method for property <tt>judgeValue</tt>.
     *
     * @param judgeValue value to be assigned to property judgeValue
     */
    public void setJudgeValue(String judgeValue) {
        this.judgeValue = judgeValue;
    }

    /**
     * Getter method for property <tt>times</tt>.
     *
     * @return property value of times
     */

    public int getTimes() {
        return times;
    }

    /**
     * Setter method for property <tt>times</tt>.
     *
     * @param times value to be assigned to property times
     */
    public void setTimes(int times) {
        this.times = times;
    }
}
