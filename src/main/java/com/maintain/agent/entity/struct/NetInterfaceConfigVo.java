package com.maintain.agent.entity.struct;

import org.hyperic.sigar.NetInterfaceConfig;

/**
 * <AUTHOR>
 * @date 2019-01-24
 */
public class NetInterfaceConfigVo extends NetInterfaceConfig {
    String name = null;
    String hwaddr = null;
    String type = null;
    String description = null;
    String address = null;
    String destination = null;
    String broadcast = null;
    String netmask = null;
    long flags ;
    long mtu ;
    long metric ;
    String displayName;

    @Override
    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getHwaddr() {
        return hwaddr;
    }

    /**
     * Setter method for property <tt>hwaddr</tt>.
     *
     * @param hwaddr value to be assigned to property hwaddr
     */
    public void setHwaddr(String hwaddr) {
        this.hwaddr = hwaddr;
    }

    @Override
    public String getType() {
        return type;
    }

    /**
     * Setter method for property <tt>type</tt>.
     *
     * @param type value to be assigned to property type
     */
    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String getDescription() {
        return description;
    }

    /**
     * Setter method for property <tt>description</tt>.
     *
     * @param description value to be assigned to property description
     */
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String getAddress() {
        return address;
    }

    /**
     * Setter method for property <tt>address</tt>.
     *
     * @param address value to be assigned to property address
     */
    public void setAddress(String address) {
        this.address = address;
    }

    @Override
    public String getDestination() {
        return destination;
    }

    /**
     * Setter method for property <tt>destination</tt>.
     *
     * @param destination value to be assigned to property destination
     */
    public void setDestination(String destination) {
        this.destination = destination;
    }

    @Override
    public String getBroadcast() {
        return broadcast;
    }

    /**
     * Setter method for property <tt>broadcast</tt>.
     *
     * @param broadcast value to be assigned to property broadcast
     */
    public void setBroadcast(String broadcast) {
        this.broadcast = broadcast;
    }

    @Override
    public String getNetmask() {
        return netmask;
    }

    /**
     * Setter method for property <tt>netmask</tt>.
     *
     * @param netmask value to be assigned to property netmask
     */
    public void setNetmask(String netmask) {
        this.netmask = netmask;
    }

    @Override
    public long getFlags() {
        return flags;
    }

    /**
     * Setter method for property <tt>flags</tt>.
     *
     * @param flags value to be assigned to property flags
     */
    public void setFlags(long flags) {
        this.flags = flags;
    }

    @Override
    public long getMtu() {
        return mtu;
    }

    /**
     * Setter method for property <tt>mtu</tt>.
     *
     * @param mtu value to be assigned to property mtu
     */
    public void setMtu(long mtu) {
        this.mtu = mtu;
    }

    @Override
    public long getMetric() {
        return metric;
    }

    /**
     * Setter method for property <tt>metric</tt>.
     *
     * @param metric value to be assigned to property metric
     */
    public void setMetric(long metric) {
        this.metric = metric;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * Setter method for property <tt>displayName</tt>.
     *
     * @param displayName value to be assigned to property displayName
     */
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
}