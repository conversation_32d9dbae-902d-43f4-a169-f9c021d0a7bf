package com.maintain.agent.entity.config;

import com.maintain.agent.utils.DataCheckUtil;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2016/10/31
 * 系统配置项
 */
public class SystemConfig {

    private Integer autoNtp;
    /**
     * Agent端程序名
     */
    private String workDirName;

    /**
     * 运维Server端的Ip
     */
    private String maintainServerIp;

    /**
     *  ntp服务器地址
     */
    private String ntpServer;
    /**
     * 运维Agent端的地址
     */
    private String maintainAgent;
    /**
     * 向Server端发送心跳的间隔（单位：毫秒）
     */
    private int heartbeatInterval;
    /**
     * 更新内部缓存的间隔（单位：毫秒）
     */
    private int updateCacheInterval;
    /**
     * 发送硬件信息的时间间隔（秒）
     */
    private int sendMonitorTime;
    /**
     * 需要过滤的系统程序名
     */
    private Set<String> sysProcNames;
    /**
     * 运维系统监控的程序名
     */
    private List<String> userProcNames;
    /**
     * ftp的url前缀
     */
    private String ftpPrefix;
    /**
     * ftp的文件目录
     */
    private String ftpDir;
    /**
     * ftp的端口
     */
    private int ftpPort;
    /**
     * 日志文件预览的字节数
     */
    private long browseBytes;

    /**
     * agent生成/读取主机硬件监控的文件路径(windows)
     */
    private String hardwareMonitoringDirWindow;
    /**
     * agent生成/读取主机硬件监控的文件路径(linux)
     */
    private String hardwareMonitoringDirLinux;
    /**
     * agent读取软件系统监控的文件路径(windows)
     */
    private String softwareMonitoringDirWindow;
    /**
     * agent读取软件系统监控的文件路径(linux)
     */
    private String softwareMonitoringDirLinux;

    /**
     * 验证进程的关键字
     */
    private List<String> validateKeys;

    private long monitorTime;

    public long getMonitorTime() {
        return monitorTime;
    }

    public void setMonitorTime(long monitorTime) {
        this.monitorTime = monitorTime;
    }

    /**
     * PYTHON安装路径
     */
    private String pythonPath = "C:\\ProgramData\\Anaconda3\\python.exe";

    private List<String> configPathList = Arrays.asList("conf", "config");

    /**
     * Getter method for property <tt>workDirName</tt>.
     *
     * @return property value of workDirName
     */

    public String getWorkDirName() {
        return workDirName;
    }

    /**
     * Setter method for property <tt>workDirName</tt>.
     *
     * @param workDirName value to be assigned to property workDirName
     */
    public void setWorkDirName(String workDirName) {
        this.workDirName = workDirName;
    }

    /**
     * Getter method for property <tt>sendHardInfoTime</tt>.
     *
     * @return property value of sendHardInfoTime
     */
    public int getSendMonitorTime() {
        return sendMonitorTime;
    }

    /**
     * Setter method for property <tt>sendMonitorTime</tt>.
     *
     * @param sendMonitorTime value to be assigned to property sendMonitorTime
     */
    public void setSendMonitorTime(int sendMonitorTime) {
        this.sendMonitorTime = sendMonitorTime;
    }


    /**
     * Getter method for property <tt>maintainAgent</tt>.
     *
     * @return property value of maintainAgent
     */

    public String getMaintainAgent() {
        return maintainAgent;
    }

    /**
     * Setter method for property <tt>maintainAgent</tt>.
     *
     * @param maintainAgent value to be assigned to property maintainAgent
     */
    public void setMaintainAgent(String maintainAgent) {
        this.maintainAgent = maintainAgent;
    }

    /**
     * Getter method for property <tt>heartbeatInterval</tt>.
     *
     * @return property value of heartbeatInterval
     */

    public int getHeartbeatInterval() {
        return heartbeatInterval;
    }

    /**
     * Setter method for property <tt>heartbeatInterval</tt>.
     *
     * @param heartbeatInterval value to be assigned to property heartbeatInterval
     */
    public void setHeartbeatInterval(int heartbeatInterval) {
        this.heartbeatInterval = heartbeatInterval;
    }

    /**
     * Getter method for property <tt>updateCacheInterval</tt>.
     *
     * @return property value of updateCacheInterval
     */

    public int getUpdateCacheInterval() {
        return updateCacheInterval;
    }

    /**
     * Setter method for property <tt>updateCacheInterval</tt>.
     *
     * @param updateCacheInterval value to be assigned to property updateCacheInterval
     */
    public void setUpdateCacheInterval(int updateCacheInterval) {
        this.updateCacheInterval = updateCacheInterval;
    }

    /**
     * Getter method for property <tt>sysProcNames</tt>.
     *
     * @return property value of sysProcNames
     */

    public Set<String> getSysProcNames() {
        return sysProcNames;
    }

    /**
     * Setter method for property <tt>sysProcNames</tt>.
     *
     * @param sysProcNames value to be assigned to property sysProcNames
     */
    public void setSysProcNames(Set<String> sysProcNames) {
        this.sysProcNames = sysProcNames;
    }

    /**
     * Getter method for property <tt>userProcNames</tt>.
     *
     * @return property value of userProcNames
     */

    public List<String> getUserProcNames() {
        return userProcNames;
    }

    public List<String> getValidateKeys() {
        return validateKeys;
    }

    public void setValidateKeys(List<String> validateKeys) {
        this.validateKeys = validateKeys;
    }

    /**
     * Setter method for property <tt>userProcNames</tt>.
     *
     * @param userProcNames value to be assigned to property userProcNames
     */
    public void setUserProcNames(List<String> userProcNames) {
        this.userProcNames = userProcNames;
    }

    /**
     * Getter method for property <tt>ftpPrefix</tt>.
     *
     * @return property value of ftpPrefix
     */

    public String getFtpPrefix() {
        return ftpPrefix;
    }

    /**
     * Setter method for property <tt>ftpPrefix</tt>.
     *
     * @param ftpPrefix value to be assigned to property ftpPrefix
     */
    public void setFtpPrefix(String ftpPrefix) {
        this.ftpPrefix = ftpPrefix;
    }

    /**
     * Getter method for property <tt>ftpDir</tt>.
     *
     * @return property value of ftpDir
     */

    public String getFtpDir() {
        return ftpDir;
    }

    /**
     * Setter method for property <tt>ftpDir</tt>.
     *
     * @param ftpDir value to be assigned to property ftpDir
     */
    public void setFtpDir(String ftpDir) {
        this.ftpDir = ftpDir;
    }

    /**
     * Getter method for property <tt>ftpPort</tt>.
     *
     * @return property value of ftpPort
     */

    public int getFtpPort() {
        return ftpPort;
    }

    /**
     * Setter method for property <tt>ftpPort</tt>.
     *
     * @param ftpPort value to be assigned to property ftpPort
     */
    public void setFtpPort(int ftpPort) {
        this.ftpPort = ftpPort;
    }

    /**
     * Getter method for property <tt>browseBytes</tt>.
     *
     * @return property value of browseBytes
     */

    public long getBrowseBytes() {
        return browseBytes;
    }

    /**
     * Setter method for property <tt>browseBytes</tt>.
     *
     * @param browseBytes value to be assigned to property browseBytes
     */
    public void setBrowseBytes(long browseBytes) {
        this.browseBytes = browseBytes;
    }

    /**
     * Getter method for property <tt>hardwareMonitoringDirWindow</tt>.
     *
     * @return property value of hardwareMonitoringDirWindow
     */

    public String getHardwareMonitoringDirWindow() {
        return hardwareMonitoringDirWindow;
    }

    /**
     * Setter method for property <tt>hardwareMonitoringDirWindow</tt>.
     *
     * @param hardwareMonitoringDirWindow value to be assigned to property hardwareMonitoringDirWindow
     */
    public void setHardwareMonitoringDirWindow(String hardwareMonitoringDirWindow) {
        this.hardwareMonitoringDirWindow = hardwareMonitoringDirWindow;
    }

    /**
     * Getter method for property <tt>hardwareMonitoringDirLinux</tt>.
     *
     * @return property value of hardwareMonitoringDirLinux
     */

    public String getHardwareMonitoringDirLinux() {
        return hardwareMonitoringDirLinux;
    }

    /**
     * Setter method for property <tt>hardwareMonitoringDirLinux</tt>.
     *
     * @param hardwareMonitoringDirLinux value to be assigned to property hardwareMonitoringDirLinux
     */
    public void setHardwareMonitoringDirLinux(String hardwareMonitoringDirLinux) {
        this.hardwareMonitoringDirLinux = hardwareMonitoringDirLinux;
    }

    /**
     * Getter method for property <tt>softwareMonitoringDirWindow</tt>.
     *
     * @return property value of softwareMonitoringDirWindow
     */

    public String getSoftwareMonitoringDirWindow() {
        return softwareMonitoringDirWindow;
    }

    /**
     * Setter method for property <tt>softwareMonitoringDirWindow</tt>.
     *
     * @param softwareMonitoringDirWindow value to be assigned to property softwareMonitoringDirWindow
     */
    public void setSoftwareMonitoringDirWindow(String softwareMonitoringDirWindow) {
        this.softwareMonitoringDirWindow = softwareMonitoringDirWindow;
    }

    /**
     * Getter method for property <tt>softwareMonitoringDirLinux</tt>.
     *
     * @return property value of softwareMonitoringDirLinux
     */

    public String getSoftwareMonitoringDirLinux() {
        return softwareMonitoringDirLinux;
    }

    /**
     * Setter method for property <tt>softwareMonitoringDirLinux</tt>.
     *
     * @param softwareMonitoringDirLinux value to be assigned to property softwareMonitoringDirLinux
     */
    public void setSoftwareMonitoringDirLinux(String softwareMonitoringDirLinux) {
        this.softwareMonitoringDirLinux = softwareMonitoringDirLinux;
    }

    public String getMaintainServerIp() {
        return maintainServerIp;
    }

    /**
     * Setter method for property <tt>maintainServerIp</tt>.
     *
     * @param maintainServerIp value to be assigned to property maintainServerIp
     */
    public void setMaintainServerIp(String maintainServerIp) {
        this.maintainServerIp = maintainServerIp;
    }

    public Integer getAutoNtp() {
        return autoNtp;
    }

    /**
     * Setter method for property <tt>autoNtp</tt>.
     *
     * @param autoNtp value to be assigned to property autoNtp
     */
    public void setAutoNtp(Integer autoNtp) {
        this.autoNtp = autoNtp;
    }

    public String getPythonPath() {
        return pythonPath;
    }

    public void setPythonPath(String pythonPath) {
        this.pythonPath = pythonPath;
    }

    public List<String> getConfigPathList() {
        return configPathList;
    }

    public void setConfigPathList(List<String> configPathList) {
        this.configPathList = configPathList;
    }

    public String getNtpServer() {
        return ntpServer;
    }

    public void setNtpServer(String ntpServer) {
        this.ntpServer = ntpServer;
    }

    /**
     * 判断配置是否有效
     *
     * @return 返回是否有效
     */
    public boolean isValid() {
        if (workDirName == null || workDirName.isEmpty()) {
            return false;
        }
        if (maintainServerIp == null || maintainServerIp.isEmpty()) {
            return false;
        }
        if (ntpServer == null || ntpServer.isEmpty()) {
            return false;
        }
        if (maintainAgent == null || maintainAgent.isEmpty()) {
            return false;
        }
        if (heartbeatInterval <= 0 || updateCacheInterval <= 0) {
            return false;
        }
        if (sysProcNames == null || sysProcNames.isEmpty()) {
            return false;
        }
        if (userProcNames == null || userProcNames.isEmpty()) {
            return false;
        }
        if (ftpPrefix == null || ftpPrefix.isEmpty()) {
            return false;
        }
        if (ftpDir == null || ftpDir.isEmpty()) {
            return false;
        }
        if (ftpPort < 0 || ftpPort > 65535) {
            return false;
        }
        if (browseBytes <= 0) {
            return false;
        }

        return true;
    }
}

