package com.maintain.agent.entity;

/**
 * <AUTHOR>
 * @date 2020-12-21
 */
public class Network {
    /**
     * 时间戳，秒
     */
    private long timestamp;
    /**
     * 收到的字节
     */
    private long receiveBytes;
    /**
     * 发送的字节
     */
    private long sendBytes;
    /**
     * 网卡名称
     */
    private String name;
    /**
     * 时间间隔，秒
     */
    private long interval;


    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    public long getReceiveBytes() {
        return receiveBytes;
    }

    public void setReceiveBytes(long receiveBytes) {
        this.receiveBytes = receiveBytes;
    }

    public long getSendBytes() {
        return sendBytes;
    }

    public void setSendBytes(long sendBytes) {
        this.sendBytes = sendBytes;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public long getInterval() {
        return interval;
    }

    public void setInterval(long interval) {
        this.interval = interval;
    }
}