package com.maintain.agent.entity;

/**
 * <AUTHOR>
 * @date 2020-07-07
 */

public class MysqlBackupVo {
    /**
     * 备份时间
     */
    private String backupTime;

    /**
     * 0-按天，1-按周
     */
    private int backupType;

    /**
     * 备份的数据库名称
     */
    private String backupDatabases;

    /**
     * 是否备份成功,1-成功，0-失败
     */
    private int success;
    /**
     * 备份文件所在IP
     */
    private String ip;


    public String getBackupTime() {
        return backupTime;
    }

    public void setBackupTime(String backupTime) {
        this.backupTime = backupTime;
    }

    public int getBackupType() {
        return backupType;
    }

    public void setBackupType(int backupType) {
        this.backupType = backupType;
    }

    public String getBackupDatabases() {
        return backupDatabases;
    }

    public void setBackupDatabases(String backupDatabases) {
        this.backupDatabases = backupDatabases;
    }

    public int getSuccess() {
        return success;
    }

    public void setSuccess(int success) {
        this.success = success;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }
}