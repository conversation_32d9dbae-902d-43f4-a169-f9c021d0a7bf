package com.maintain.agent.entity.request;

/**
 * <AUTHOR>
 * @date 2016/10/31
 */
public class AddressRequest {
    /**
     * ip
     */
    private String address;
    /**
     * 操作系统类型
     */
    private int os;

    /**
     * Getter method for property <tt>address</tt>.
     *
     * @return property value of address
     */

    public String getAddress() {
        return address;
    }

    /**
     * Setter method for property <tt>address</tt>.
     *
     * @param address value to be assigned to property address
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * Getter method for property <tt>os</tt>.
     *
     * @return property value of os
     */

    public int getOs() {
        return os;
    }

    /**
     * Setter method for property <tt>os</tt>.
     *
     * @param os value to be assigned to property os
     */
    public void setOs(int os) {
        this.os = os;
    }
}
