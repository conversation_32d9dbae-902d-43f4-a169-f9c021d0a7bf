package com.maintain.agent.entity.request;

/**
 * <AUTHOR>
 * @date 2016/10/27
 */
public class HostProcRequest {
    /**
     * ip
     */
    private String host;
    /**
     * 进程
     */
    private String name;

    private String path;
    private String logPath;
    private String logSuffix;
    private String configPath;

    /**
     * Getter method for property <tt>host</tt>.
     *
     * @return property value of host
     */

    public String getHost() {
        return host;
    }

    /**
     * Setter method for property <tt>host</tt>.
     *
     * @param host value to be assigned to property host
     */
    public void setHost(String host) {
        this.host = host;
    }


    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    public void setName(String name) {
        this.name = name;
    }

    public String getPath() {
        return path;
    }

    /**
     * Setter method for property <tt>path</tt>.
     *
     * @param path value to be assigned to property path
     */
    public void setPath(String path) {
        this.path = path;
    }

    public String getLogPath() {
        return logPath;
    }

    public void setLogPath(String logPath) {
        this.logPath = logPath;
    }

    public String getLogSuffix() {
        return logSuffix;
    }

    public void setLogSuffix(String logSuffix) {
        this.logSuffix = logSuffix;
    }

    public String getConfigPath() {
        return configPath;
    }

    public void setConfigPath(String configPath) {
        this.configPath = configPath;
    }
}
