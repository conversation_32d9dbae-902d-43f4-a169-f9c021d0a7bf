package com.maintain.agent;

import com.common.log.Log;
import com.maintain.agent.business.helper.BusinessHelper;
import com.maintain.agent.business.manager.MaintainAgentManager;
import com.maintain.agent.enums.OsTypeEnum;
import com.maintain.agent.utils.ExecUtil;
import org.apache.commons.io.FileUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.io.File;
import java.io.IOException;
import java.util.Arrays;

/**
 * 运维Agent端主程序
 * 该程序负责：
 * 1. 初始化运维Agent环境
 * 2. 管理Python环境的安装和配置
 * 3. 处理Windows和Linux系统下的环境变量配置
 * 
 * <AUTHOR>
 * @date 2016/10/31
 */
@SpringBootApplication
public class MaintainAgentMain {

    /**
     * 程序入口点
     * 初始化运维Agent管理器并启动Spring Boot应用
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        if (MaintainAgentManager.getInstance().init()) {
            Log.low.info("init success");
        } else {
            Log.low.info("init failed");
        }
        SpringApplication.run(MaintainAgentMain.class, args);
    }

    /**
     * 刷新系统环境变量
     * 仅在Windows系统下执行，通过调用ReadRegeditAgain.exe工具来刷新环境变量
     */
    private void flushEvnPath() {
        //只有windows需要刷新环境变量
        if (OsTypeEnum.WINDOWS.getType() == BusinessHelper.getOsType()) {
            try {
                File destFile = new File("D:\\MAINTAIN_TEMP\\baseEvn\\ReadRegeditAgain.exe");
                File toolFile = new File("./deploy/ReadRegeditAgain.exe");
                if (!destFile.exists()) {
                    FileUtils.copyFile(toolFile, destFile);
                }
                ExecUtil.execCmdOnWindows("D:\\MAINTAIN_TEMP\\baseEvn\\ReadRegeditAgain.exe");
            } catch (Exception e) {
                Log.high.error("刷新环境变量异常", e);
            }
        }
    }

    /**
     * 检查Python环境是否已正确配置
     * 通过检查系统环境变量中是否包含Python相关路径来判断
     * 
     * @param times 重试次数
     * @return 如果环境变量中包含Python路径返回true，否则返回false
     * @throws RuntimeException 当重试3次后仍然失败时抛出
     */
    private boolean checkPythonEvn(int times) {
        try {
            String[] result;
            if (OsTypeEnum.LINUX.getType() == BusinessHelper.getOsType()) {
                String command = "cat /etc/profile";
                result = ExecUtil.execCmdOnLinux(command);
            } else {
                String command = "reg query \"HKLM\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v Path";
                result = ExecUtil.execCmdOnWindows(command);
            }
            String info = result[0].toLowerCase();
            return info.contains("python3") || info.contains("anaconda3")
                    || info.contains("python-3.6.5");
        } catch (IOException e) {
            if (times > 3) {
                throw new RuntimeException("查看环境变量出错");
            }
            times++;
            return checkPythonEvn(times);
        }
    }

    /**
     * 安装Python环境
     * 根据操作系统类型选择相应的安装方法
     * 如果检测到已安装Python则跳过安装
     */
    private void installPythonEvn() {
        try {
            boolean r = checkPythonEvn(0);
            //没有检测到python的环境变量，则进行安装python
            if (!r) {
                if (OsTypeEnum.LINUX.getType() == BusinessHelper.getOsType()) {
                    installPythonOnLocalLinux();
                } else {
                    installPythonOnLocalWindows();
                }
            }
        } catch (Exception e) {
            Log.high.error("install python error:" + e);
        }
    }

    /**
     * 在Windows系统上安装Python环境
     * 安装步骤：
     * 1. 检查安装包是否存在
     * 2. 静默安装Python
     * 3. 配置系统环境变量
     * 4. 刷新环境变量
     * 
     * @throws IOException 当安装过程出现IO异常时抛出
     * @throws RuntimeException 当安装包不存在时抛出
     */
    private void installPythonOnLocalWindows() throws IOException {
        long start = System.currentTimeMillis();
        String remotePythonPath = "D:\\MAINTAIN_TEMP\\baseEvn\\python-3.6.5-amd64.exe";
        String remoteToolPath = "D:\\MAINTAIN_TEMP\\baseEvn\\ReadRegeditAgain.exe";
        if (!new File(remotePythonPath).exists() || !new File(remoteToolPath).exists()) {
            throw new RuntimeException("指定路径上需要的安装包不存在或缺失");
        }
        StringBuilder command = new StringBuilder();
        command.append("D:\\MAINTAIN_TEMP\\baseEvn\\python-3.6.5-amd64.exe /q")
                .append(" && ")
                .append("reg add \"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Control\\Session Manager\\Environment\" /v Path /t REG_EXPAND_SZ /d \"%Path%;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python36;\" /f")
                .append(" && ")
                .append("D:\\MAINTAIN_TEMP\\baseEvn\\ReadRegeditAgain.exe");
        String result[] = ExecUtil.execCmdOnWindows(command.toString());
        Log.low.info("exec result:" + Arrays.toString(result));
        Log.low.info("安装PYTHON环境耗时(ms):" + (System.currentTimeMillis() - start));
    }

    /**
     * 在Linux系统上安装Python环境
     * 安装步骤：
     * 1. 检查安装包是否存在
     * 2. 解压Python安装包到/usr/local目录
     * 3. 配置Python环境变量到/etc/profile
     * 4. 使环境变量生效
     * 
     * @throws IOException 当安装过程出现IO异常时抛出
     * @throws RuntimeException 当安装包不存在时抛出
     */
    private void installPythonOnLocalLinux() throws IOException {
        //此路径下的安装文件由server端上传
        String tempPath = "/dist/MAINTAIN_TEMP/baseEvn/Python-3.6.5.tar.gz";
        File pythonFile = new File(tempPath);
        if (!pythonFile.exists()) {
            throw new RuntimeException("指定路径上不存在安装包");
        }
        StringBuilder command = new StringBuilder();
        command.append("tar -zxf /dist/MAINTAIN_TEMP/baseEvn/Python-3.6.5.tar.gz -C /usr/local")
                .append(" && ")
                .append("echo \"export PYTHON_HOME=/usr/local/Python-3.6.5\" >> /etc/profile")
                .append(" && ")
                .append("echo \"export PATH=\\$PYTHON_HOME:\\$PATH\" >> /etc/profile");
        ExecUtil.execCmdOnLinux(command.toString());
        ExecUtil.execCmdOnLinux("source /etc/profile");
    }
}
