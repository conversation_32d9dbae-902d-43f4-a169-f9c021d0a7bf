package com.maintain.agent.enums;

/**
 * Created by Azurio on 2018/11/30.
 * 程序操作
 */
public enum SoftwareOperateEnum {

    CLOSE(0, "关闭"), OPEN(1, "开启"), RELOAD(2, "重启");

    private Integer id;
    private String name;

    SoftwareOperateEnum(Integer id, String name) {
        this.id = id;
        this.name = name;
    }

    public Integer getId() {
        return id;
    }

    /**
     * Setter method for property <tt>id</tt>.
     *
     * @param id value to be assigned to property id
     */
    private void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    /**
     * Setter method for property <tt>name</tt>.
     *
     * @param name value to be assigned to property name
     */
    private void setName(String name) {
        this.name = name;
    }
}
