package com.maintain.agent.enums;

/**
 * <AUTHOR>
 * @date 2018-01-29
 * 操作系统类型
 */
public enum OsTypeEnum {
    /**
     * linux（1）
     */
    LINUX(1),

    /**
     * windows（0）
     */
    WINDOWS(0),

    /**
     * other（-1）
     */
    OTHER(-1);

    /**
     * 类型
     */
    private int type;

    OsTypeEnum(int type) {
        this.type = type;
    }

    /**
     * 获取类型
     *
     * @return 返回类型
     */
    public int getType() {
        return type;
    }
}
