package com.maintain.agent.utils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

/**
 * 日期时间工具类
 * 提供日期时间的格式化、解析和计算功能，包括：
 * 1. 日期时间的格式化输出
 * 2. 字符串与Date对象的相互转换
 * 3. 日期时间的计算和调整
 * 4. 线程安全的日期格式化处理
 * 
 * 主要功能：
 * - 提供多种日期时间格式的转换
 * - 支持时间戳和Date对象的格式化
 * - 提供日期时间的加减计算
 * - 使用ThreadLocal确保线程安全
 * - 支持自定义日期格式
 * - 提供当前时间的获取和格式化
 * 
 * <AUTHOR>
 * @date 15-10-15
 */
public final class DateUtil {
    private DateUtil() {
    }

    /**
     * 日期时间格式：yyyy-MM-dd HH:mm:ss:SSS
     * 包含毫秒的完整日期时间格式
     */
    public static final String DATETIME = "yyyy-MM-dd HH:mm:ss:SSS";

    /**
     * 标准日期时间格式：yyyy-MM-dd HH:mm:ss
     * 不包含毫秒的日期时间格式
     */
    public static final String DATE_TIME_FORMATTER = "yyyy-MM-dd HH:mm:ss";

    /**
     * ThreadLocal实例
     * 用于存储每个线程的SimpleDateFormat实例，避免多线程并发问题
     */
    private static final ThreadLocal<SimpleDateFormat> THREAD_LOCAL = new ThreadLocal<>();

    /**
     * 线程锁对象
     * 用于同步创建SimpleDateFormat实例
     */
    private static final Object OBJECT = new Object();

    /**
     * 获取当前时间的标准格式字符串
     * 使用DATE_TIME_FORMATTER格式
     *
     * @return 当前时间的格式化字符串
     */
    public static String now() {
        return getDateFormat(DATE_TIME_FORMATTER).format(new Date());
    }

    /**
     * 获取SimpleDateFormat实例
     * 使用ThreadLocal确保线程安全
     * 支持动态修改日期格式
     *
     * @param pattern 日期格式
     * @return SimpleDateFormat实例
     */
    private static SimpleDateFormat getDateFormat(String pattern) {
        SimpleDateFormat dateFormat = THREAD_LOCAL.get();
        if (dateFormat == null) {
            synchronized (OBJECT) {
                if (dateFormat == null) {
                    dateFormat = new SimpleDateFormat(pattern);
                    dateFormat.setLenient(false);
                    THREAD_LOCAL.set(dateFormat);
                }
            }
        }
        dateFormat.applyPattern(pattern);
        return dateFormat;
    }

    /**
     * 格式化时间戳
     * 将毫秒时间戳转换为指定格式的日期时间字符串
     *
     * @param timestamp 毫秒时间戳
     * @param pattern 日期格式
     * @return 格式化后的日期时间字符串
     */
    public static String formatDate(long timestamp, String pattern) {
        return getDateFormat(pattern).format(timestamp);
    }

    /**
     * 格式化Date对象
     * 将Date对象转换为指定格式的日期时间字符串
     *
     * @param date Date对象
     * @param pattern 日期格式
     * @return 格式化后的日期时间字符串
     */
    public static String formatDate(Date date, String pattern) {
        return getDateFormat(pattern).format(date);
    }

    /**
     * 获取当前日期时间的指定格式字符串
     *
     * @param dateType 日期格式
     * @return 当前日期时间的格式化字符串
     */
    public static String getDate(String dateType) {
        SimpleDateFormat df = new SimpleDateFormat(dateType);
        return df.format(new Date());
    }

    /**
     * 获取今天已经过去的分钟数
     * 从0点开始计算
     *
     * @return 今天已经过去的分钟数
     */
    public static int getTodayMinutes() {
        String nowTime = getDate("HH:mm");
        String[] time = nowTime.split(":");
        int hour = Integer.parseInt(time[0]);
        int minute = Integer.parseInt(time[1]);
        return hour * 60 + minute;
    }

    /**
     * 将字符串转换为Date对象
     *
     * @param date 日期时间字符串
     * @param dateType 日期格式
     * @return Date对象
     * @throws ParseException 当字符串格式不匹配时抛出
     */
    public static Date toDate(String date, String dateType) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat(dateType);
        return df.parse(date);
    }

    /**
     * 日期时间格式转换
     * 将一种格式的日期时间字符串转换为另一种格式
     *
     * @param date 原始日期时间字符串
     * @param dateType 原始格式
     * @param destType 目标格式
     * @return 转换后的日期时间字符串
     * @throws ParseException 当字符串格式不匹配时抛出
     */
    public static String toDate(String date, String dateType, String destType) throws ParseException {
        SimpleDateFormat df = new SimpleDateFormat(dateType);
        return formatDate(df.parse(date), destType);
    }

    /**
     * 日期时间计算
     * 对指定日期进行加减操作
     *
     * @param date 原始日期
     * @param util 计算单位（Calendar.DATE等）
     * @param num 计算值（正数表示加，负数表示减）
     * @return 计算后的日期
     */
    public static Date dateAdd(Date date, int util, int num) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(util, calendar.get(util) + num);
        return calendar.getTime();
    }

}
