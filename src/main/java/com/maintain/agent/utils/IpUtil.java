package com.maintain.agent.utils;

import com.common.log.Log;
import com.common.util.ObjectMapperUtil;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.FileInputStream;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020-11-20
 */
public class IpUtil {

    public static String getLocalHost() {
        try {
            String filepath = "conf/config.json";
            Map<String, Object> map = ObjectMapperUtil.objectMapper.readValue(new File(filepath), Map.class);
            Object agent = map.get("maintainAgent");
            return agent.toString().split("\\s+")[2];
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
        return getHostIP();
    }

    /**
     * 获取本机 V4 IP
     *
     * @return String
     */
    private static String getHostIP() {
        String hostIPs = "";
        try {
            Enumeration<NetworkInterface> netInterfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress ip = null;
            while (netInterfaces.hasMoreElements()) {
                NetworkInterface ni = netInterfaces.nextElement();
                if (ni.getDisplayName().contains("VMware Virtual")) {
                    continue;
                }
                Enumeration<InetAddress> inetAddresses = ni.getInetAddresses();
                String localhost = "127.0.0.1";
                while (inetAddresses.hasMoreElements()) {
                    ip = inetAddresses.nextElement();
                    if (!ip.getHostAddress().contains(localhost) && !ip.getHostAddress().contains(":")) {
                        hostIPs += ip.getHostAddress() + ",";
                    }
                }
            }
        } catch (Exception e) {
            Log.high.error("get host IP failed ...", e);
        }
        if (hostIPs.length() == 0) {
            return "notgetip";
        }
        return hostIPs.substring(0, hostIPs.length() - 1);
    }
}