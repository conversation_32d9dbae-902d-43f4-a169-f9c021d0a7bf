package com.maintain.agent.utils;

import com.maintain.agent.conf.Constants;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2016/11/3
 */
public class DataTransUtil {
    /**
     * 转化成字节数
     * 目前只能处理形如 7.2P、7.2T、7.2G、7.2M、7.2K、7.2B 的情况
     * 暂不支持形如 7.2G7.2M、7.2GB7.2MB 的情况
     *
     * @param value 字符串
     * @return 与字符串等价的字节数
     */
    public static long toBytesLongFormat(String value) {

        value = value.toUpperCase();
        if (value.endsWith("P")) {
            float num = Float.parseFloat(value.substring(0, value.length() - 1));
            return (long) (num * Constants.PB_SIZE);
        }
        if (value.endsWith("T")) {
            float num = Float.parseFloat(value.substring(0, value.length() - 1));
            return (long) (num * Constants.TB_SIZE);
        }
        if (value.endsWith("G")) {
            float num = Float.parseFloat(value.substring(0, value.length() - 1));
            return (long) (num * Constants.GB_SIZE);
        }
        if (value.endsWith("M")) {
            float num = Float.parseFloat(value.substring(0, value.length() - 1));
            return (long) (num * Constants.MB_SIZE);
        }
        if (value.endsWith("K")) {
            float num = Float.parseFloat(value.substring(0, value.length() - 1));
            return (long) (num * Constants.KB_SIZE);
        }
        if (value.endsWith("B")) {
            float num = Float.parseFloat(value.substring(0, value.length() - 1));
            return (long) num;
        }
        return 0;
    }

    /**
     * 转化成字符串形式的字节数
     *
     * @param value 字节数
     * @return 与字节数等价的字符串形式，形如 7.2G
     */
    public static String toBytesStrFormat(long value) {
        if (value <= 0) {
            return value + "B";
        }
        float k = 1024.0f;
        float result = value / k;
        //此时result的单位是K
        if (result < 1) {
            return value + "B";
        } else {
            //此时result的单位是K
            if (result < k) {
                //四舍五入到小数点后两位
                return (float) (Math.round(result * 100)) / 100 + "K";
            } else {
                result = result / k;
                //此时result的单位是M
                if (result < k) {
                    return (float) (Math.round(result * 100)) / 100 + "M";
                } else {
                    result = result / k;
                    //此时result的单位是G
                    if (result < k) {
                        return (float) (Math.round(result * 100)) / 100 + "G";
                    } else {
                        result = result / k;
                        //此时result的单位是T
                        if (result < k) {
                            return (float) (Math.round(result * 100)) / 100 + "T";
                        } else {
                            result = result / k;
                            //此时result的单位是P
                            return (float) (Math.round(result * 100)) / 100 + "P";
                        }
                    }
                }
            }
        }
    }


    /**
     * 转化成字符串映射表
     *
     * @param list 字符串数组（二元）列表
     * @return 字符串映射表
     */
    public static Map<String, String> toStrMap(List<String[]> list) {
        Map<String, String> map = new HashMap<>();
        for (String[] parts : list) {
            if (parts == null || parts.length != 2) {
                continue;
            }
            map.put(parts[0], parts[1]);
        }
        return map;
    }

    /**
     * 转化成字符串列表
     *
     * @param set 字符串集
     * @return 字符串列表
     */
    public static List<String> toStrList(Set<String> set) {
        List<String> list = new ArrayList<>(set.size());
        for (String value : set) {
            list.add(value);
        }
        return list;
    }

    /**
     * 转化成百分比字符串形式（含四舍五入）
     * 如：percent=31.33815, 返回31.34%
     *
     * @param percent 百分比
     * @return 百分比字符串形式
     */
    public static String toPercentStrFormat(double percent) {
        double result = (double) (Math.round(percent * 100.0)) / 100.0;
        return result + "%";
    }

}
