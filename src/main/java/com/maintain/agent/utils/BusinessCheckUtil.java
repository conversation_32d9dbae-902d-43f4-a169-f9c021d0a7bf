package com.maintain.agent.utils;


import com.maintain.agent.business.service.HardInfo;
import com.maintain.agent.conf.Config;
import com.maintain.agent.entity.struct.NetCardInfoStruct;
import com.maintain.agent.entity.struct.NetInterfaceConfigVo;
import com.common.log.Log;

import java.io.File;
import java.io.IOException;
import java.net.*;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2016/10/28
 * 业务检查工具类
 */
public class BusinessCheckUtil {

    public static List<NetCardInfoStruct> getNetCardInfoList() {
        try {
            List<NetCardInfoStruct> list = new ArrayList<>();
            Iterator iterator = NetworkUtils.getInterfaces().iterator();
            while (iterator.hasNext()) {
                NetworkInterface nic = (NetworkInterface) iterator.next();
                NetCardInfoStruct netCardInfoStruct = new NetCardInfoStruct();
                NetInterfaceConfigVo config = new NetInterfaceConfigVo();
                list.add(netCardInfoStruct);
                netCardInfoStruct.setNetInterfaceConfig(config);
                config.setName(nic.getName());
                StringBuilder msg = new StringBuilder();
                if (!nic.getName().equals(nic.getDisplayName())) {
                    msg.append(nic.getDisplayName());
                    msg.append(System.lineSeparator());
                }
                setMsg(nic, msg);
                config.setDescription(msg.toString());
                List addresses = nic.getInterfaceAddresses();
                Iterator hardware = addresses.iterator();
                InterfaceAddress i;
                while (hardware.hasNext()) {
                    i = (InterfaceAddress) hardware.next();
                    InetAddress address = i.getAddress();
                    if (!(address instanceof Inet6Address)) {
                        config.setAddress(format(address));
                    }
                }
                byte[] var6 = nic.getHardwareAddress();
                if (var6 != null) {
                    StringBuilder hwaddr = new StringBuilder();
                    for (int var7 = 0; var7 < var6.length; ++var7) {
                        if (var7 > 0) {
                            hwaddr.append(":");
                        }
                        hwaddr.append(String.format(Locale.ROOT, "%02X", new Object[]{Byte.valueOf(var6[var7])}));
                    }
                    config.setHwaddr(hwaddr.toString());
                }
            }
            return list;
        } catch (Exception e) {
            Log.high.error("get netCard info error.", e);
            return new ArrayList<>();
        }
    }

    private static void setMsg(NetworkInterface nic, StringBuilder msg) throws IOException {
        List addresses = nic.getInterfaceAddresses();
        Iterator hardware = addresses.iterator();
        InterfaceAddress i;
        while (hardware.hasNext()) {
            i = (InterfaceAddress) hardware.next();
            msg.append("        ");
            msg.append(formatAddress(i));
            msg.append(System.lineSeparator());
        }

        byte[] var6 = nic.getHardwareAddress();
        if (var6 != null) {
            msg.append("        ");
            msg.append("hardware ");

            for (int var7 = 0; var7 < var6.length; ++var7) {
                if (var7 > 0) {
                    msg.append(":");
                }

                msg.append(String.format(Locale.ROOT, "%02X", new Object[]{Byte.valueOf(var6[var7])}));
            }

            msg.append(System.lineSeparator());
        }
        msg.append("        ");
        msg.append(formatFlags(nic));
        msg.append(System.lineSeparator());
    }

    /**
     * 检查字符串中是否含有有效主机IP
     *
     * @param value 字符串值
     * @return 是否含有有效主机IP
     */
    public static boolean checkHost(String value) {
        String ip = HardInfo.getInstance().getIp();
        if (ip == null || ip.isEmpty()) {
            return false;
        }
        return ip.equals(value);
    }

    /**
     * 检查字符串中是否含有有效的用户进程名称
     * value="D:\dist\FileThreatScan-1", procName="FileThreatScan"
     *
     * @param value 字符串值
     * @return 是否含有有效用户进程名称
     */
    public static boolean checkUserProc(String value) {
        List<String> procNames = Config.getSystemConfig().getUserProcNames();
        for (String procName : procNames) {
            if (value.contains(procName)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查字符串中是否为有效的Agent进程名称
     *
     * @param value 字符串值
     * @return 是否为有效Agent进程名称
     */
    public static boolean checkAgentProc(String value) {
        String agentWorkDir = Config.getSystemConfig().getWorkDirName();
        return agentWorkDir.contains(value);
    }

    /**
     * 获取Ice服务的两条信息（服务的目录、服务的名称）
     *
     * @param value 字符串值
     * @return Ice服务的两条信息
     */
    public static String[] getIceService(String value) {
        List<String> procNames = Config.getSystemConfig().getUserProcNames();
        for (String procName : procNames) {
            String iceServiceName = DataCheckUtil.hasPattern(value, procName + "(-\\d)*");
            if (iceServiceName == null || iceServiceName.isEmpty()) {
                continue;
            }
            int index;
            if ((index = value.indexOf(iceServiceName)) == -1) {
                continue;
            }
            String iceServiceDir = value.substring(0, index + iceServiceName.length());
            return new String[]{iceServiceName, iceServiceDir};
        }
        return null;
    }

    /**
     * 获取路径中的文件名
     *
     * @param path 文件的路径（绝对路径）
     * @return 文件名
     */
    public static String getFileName(String path) {
        int sepIndex = path.lastIndexOf(File.separator);
        if (sepIndex == -1) {
            return path;
        }
        return path.substring(sepIndex + 1);
    }


    private static String getHardwareInfos() throws IOException {
        StringBuilder msg = new StringBuilder();
        Iterator iterator = NetworkUtils.getInterfaces().iterator();
        while (iterator.hasNext()) {
            NetworkInterface nic = (NetworkInterface) iterator.next();
            msg.append(System.lineSeparator());
            msg.append(nic.getName());
            msg.append(System.lineSeparator());
            if (!nic.getName().equals(nic.getDisplayName())) {
                msg.append("        ");
                msg.append(nic.getDisplayName());
                msg.append(System.lineSeparator());
            }
            setMsg(nic, msg);
        }
        return msg.toString();
    }

    private static String formatAddress(InterfaceAddress interfaceAddress) throws IOException {
        StringBuilder sb = new StringBuilder();
        InetAddress address = interfaceAddress.getAddress();
        if (address instanceof Inet6Address) {
            sb.append("inet6 ");
            sb.append(format(address));
            sb.append(" prefixlen:");
            sb.append(interfaceAddress.getNetworkPrefixLength());
        } else {
            sb.append("inet ");
            sb.append(format(address));
            int netmask = -1 << 32 - interfaceAddress.getNetworkPrefixLength();
            sb.append(" netmask:" + format(InetAddress.getByAddress(new byte[]{(byte) (netmask >>> 24), (byte) (netmask >>> 16 & 255), (byte) (netmask >>> 8 & 255), (byte) (netmask & 255)})));
            InetAddress broadcast = interfaceAddress.getBroadcast();
            if (broadcast != null) {
                sb.append(" broadcast:" + format(broadcast));
            }
        }

        if (address.isLoopbackAddress()) {
            sb.append(" scope:host");
        } else if (address.isLinkLocalAddress()) {
            sb.append(" scope:link");
        } else if (address.isSiteLocalAddress()) {
            sb.append(" scope:site");
        }

        return sb.append(" ").toString();
    }

    public static String format(InetAddress address) {
        return format(address, -1);
    }

    public static String format(InetSocketAddress address) {
        return format(address.getAddress(), address.getPort());
    }

    static String format(InetAddress address, int port) {
        Objects.requireNonNull(address);
        StringBuilder builder = new StringBuilder();
        if (port != -1 && address instanceof Inet6Address) {
            builder.append(InetAddressess.toUriString(address));
        } else {
            builder.append(InetAddressess.toAddrString(address));
        }

        if (port != -1) {
            builder.append(':');
            builder.append(port);
        }

        return builder.toString();
    }

    private static String formatFlags(NetworkInterface nic) throws SocketException {
        StringBuilder flags = new StringBuilder();
        if (nic.isUp()) {
            flags.append("UP ");
        }

        if (nic.supportsMulticast()) {
            flags.append("MULTICAST ");
        }

        if (nic.isLoopback()) {
            flags.append("LOOPBACK ");
        }

        if (nic.isPointToPoint()) {
            flags.append("POINTOPOINT ");
        }

        if (nic.isVirtual()) {
            flags.append("VIRTUAL ");
        }

        flags.append("mtu:" + nic.getMTU());
        flags.append(" index:" + nic.getIndex());
        return flags.toString();
    }
}