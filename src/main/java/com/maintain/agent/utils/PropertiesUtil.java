package com.maintain.agent.utils;

import com.maintain.agent.conf.Constants;
import com.common.log.Log;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.util.*;

/**
 * Properties文件操作工具类
 * 提供Properties配置文件的读取和解析功能，包括：
 * 1. 从classpath加载Properties文件
 * 2. 读取指定键的配置值
 * 3. 解析INI格式的配置文件
 * 
 * 主要功能：
 * - 支持UTF-8编码的Properties文件读取
 * - 支持GBK编码的INI文件解析
 * - 提供批量加载和单个键值读取
 * - 支持INI文件的节（section）解析
 * 
 * <AUTHOR>
 */
public final class PropertiesUtil  {
    private PropertiesUtil() {
    }

    /**
     * 从Properties文件中加载指定键列表的值
     * 支持UTF-8编码的Properties文件
     * 如果键不存在则返回空字符串
     *
     * @param keyList 要加载的键列表
     * @param path Properties文件在classpath中的路径
     * @return 键值对Map，key为配置键，value为配置值
     */
    public static Map<String, String> loadProperties(List<String> keyList, String path) {
        Map<String, String> result = new HashMap<String, String>();
        if (ListUtil.isEmpty(keyList)) {
            return result;
        }
        Properties pro = new Properties();
        InputStream in = PropertiesUtil.class.getClassLoader().getResourceAsStream(path);
        if (in != null) {
            try {
                pro.load(new BufferedReader(new InputStreamReader(in, "UTF-8")));
                for (String key : keyList) {
                    Object obj = pro.get(key);
                    if (obj != null) {
                        String value = null;
                        value = (String) obj;
                        //value = value.replace("\"", "").trim();
                        result.put(key, value);
                    } else {
                        result.put(key, "");
                    }
                }
                return result;
            } catch (IOException e) {
                Log.high.error(e);
                return result;
            } finally {
                FileUtil.closeInputStream(in);
                pro.clear();
            }
        }
        return result;
    }

    /**
     * 从Properties文件中加载单个键的值
     * 支持UTF-8编码的Properties文件
     * 如果键不存在则返回空字符串
     *
     * @param key 要加载的键
     * @param path Properties文件在classpath中的路径
     * @return 配置值，如果键不存在则返回空字符串
     */
    public static String loadProperty(String key, String path) {
        String result = "";
        List<String> list = new ArrayList<String>();
        list.add(key);
        Map<String, String> map = loadProperties(list, path);
        if (!map.isEmpty()) {
            result = map.get(key);
        }
        return result;
    }

    /**
     * 解析INI格式的配置文件
     * 支持GBK编码的INI文件
     * 按节（section）解析配置项
     * 每个节以"OneItem"开头，以空行结束
     *
     * @param file INI配置文件
     * @return 配置节列表，每个节是一个Map，包含该节的所有键值对
     * @throws IOException 当读取文件失败时抛出
     */
    public static List<Map<String,Object>> parseIni(File file) throws IOException {
        List<Map<String, Object>> mapList = new ArrayList<>();
        BufferedReader reader = null;
        try {
            reader = new BufferedReader(new
                    InputStreamReader(new FileInputStream(file), Constants.GBK));
            Map<String,Object>  temp = null;
            String str;
            while ((str = reader.readLine()) != null) {
                if (str.contains("OneItem")) {
                    temp = new HashMap<>();
                    continue;
                }
                if (StringUtils.isEmpty(str)) {
                    //再读一行
                    mapList.add(temp);
                    temp = new HashMap<>();
                } else {
                    String[] strs = str.split("=");
                    if (strs.length > 1) {
                        if (temp != null) {
                            temp.put(strs[0], strs[1]);
                        }
                    }
                }
            }
        } catch (IOException e) {
            Log.high.error(e);
        } finally {
            if(reader != null){
                reader.close();
            }
        }
        return mapList;
    }
}
