package com.maintain.agent.utils;

import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2019-01-23
 */
public class InetAddressess {
    private static int IPV4_PART_COUNT = 4;
    private static int IPV6_PART_COUNT = 8;

    public InetAddressess() {
    }

    public static boolean isInetAddress(String ipString) {
        return ipStringToBytes(ipString) != null;
    }

    private static byte[] ipStringToBytes(String ipString) {
        boolean hasColon = false;
        boolean hasDot = false;

        for(int i = 0; i < ipString.length(); ++i) {
            char c = ipString.charAt(i);
            if(c == 46) {
                hasDot = true;
            } else if(c == 58) {
                if(hasDot) {
                    return null;
                }

                hasColon = true;
            } else if(Character.digit(c, 16) == -1) {
                return null;
            }
        }

        if(hasColon) {
            if(hasDot) {
                ipString = convertDottedQuadToHex(ipString);
                if(ipString == null) {
                    return null;
                }
            }

            return textToNumericFormatV6(ipString);
        } else if(hasDot) {
            return textToNumericFormatV4(ipString);
        } else {
            return null;
        }
    }

    private static String convertDottedQuadToHex(String ipString) {
        int lastColon = ipString.lastIndexOf(58);
        String initialPart = ipString.substring(0, lastColon + 1);
        String dottedQuad = ipString.substring(lastColon + 1);
        byte[] quad = textToNumericFormatV4(dottedQuad);
        if(quad == null) {
            return null;
        } else {
            String penultimate = Integer.toHexString((quad[0] & 255) << 8 | quad[1] & 255);
            String ultimate = Integer.toHexString((quad[2] & 255) << 8 | quad[3] & 255);
            return initialPart + penultimate + ":" + ultimate;
        }
    }

    private static byte[] textToNumericFormatV4(String ipString) {
        String[] address = ipString.split("\\.", IPV4_PART_COUNT + 1);
        if(address.length != IPV4_PART_COUNT) {
            return null;
        } else {
            byte[] bytes = new byte[IPV4_PART_COUNT];

            try {
                for(int ex = 0; ex < bytes.length; ++ex) {
                    bytes[ex] = parseOctet(address[ex]);
                }

                return bytes;
            } catch (NumberFormatException var4) {
                return null;
            }
        }
    }

    private static byte parseOctet(String ipPart) {
        int octet = Integer.parseInt(ipPart);
        if(octet <= 255 && (!ipPart.startsWith("0") || ipPart.length() <= 1)) {
            return (byte)octet;
        } else {
            throw new NumberFormatException();
        }
    }

    private static byte[] textToNumericFormatV6(String ipString) {
        String[] parts = ipString.split(":", IPV6_PART_COUNT + 2);
        if(parts.length >= 3 && parts.length <= IPV6_PART_COUNT + 1) {
            int skipIndex = -1;

            int partsHi;
            for(partsHi = 1; partsHi < parts.length - 1; ++partsHi) {
                if(parts[partsHi].length() == 0) {
                    if(skipIndex >= 0) {
                        return null;
                    }

                    skipIndex = partsHi;
                }
            }

            int partsLo;
            if(skipIndex >= 0) {
                partsHi = skipIndex;
                partsLo = parts.length - skipIndex - 1;
                if(parts[0].length() == 0) {
                    partsHi = skipIndex - 1;
                    if(partsHi != 0) {
                        return null;
                    }
                }

                if(parts[parts.length - 1].length() == 0) {
                    --partsLo;
                    if(partsLo != 0) {
                        return null;
                    }
                }
            } else {
                partsHi = parts.length;
                partsLo = 0;
            }

            int partsSkipped;
            label77: {
                partsSkipped = IPV6_PART_COUNT - (partsHi + partsLo);
                if(skipIndex >= 0) {
                    if(partsSkipped >= 1) {
                        break label77;
                    }
                } else if(partsSkipped == 0) {
                    break label77;
                }

                return null;
            }

            ByteBuffer rawBytes = ByteBuffer.allocate(2 * IPV6_PART_COUNT);

            try {
                int ex;
                for(ex = 0; ex < partsHi; ++ex) {
                    rawBytes.putShort(parseHextet(parts[ex]));
                }

                for(ex = 0; ex < partsSkipped; ++ex) {
                    rawBytes.putShort(Short.valueOf("0"));
                }

                for(ex = partsLo; ex > 0; --ex) {
                    rawBytes.putShort(parseHextet(parts[parts.length - ex]));
                }
            } catch (NumberFormatException var8) {
                return null;
            }

            return rawBytes.array();
        } else {
            return null;
        }
    }

    private static short parseHextet(String ipPart) {
        int hextet = Integer.parseInt(ipPart, 16);
        if(hextet > '\uffff') {
            throw new NumberFormatException();
        } else {
            return (short)hextet;
        }
    }

    public static String toUriString(java.net.InetAddress ip) {
        return ip instanceof Inet6Address ?"[" + toAddrString(ip) + "]":toAddrString(ip);
    }

    public static String toAddrString(java.net.InetAddress ip) {
        if(ip == null) {
            throw new NullPointerException("ip");
        } else {
            byte[] bytes;
            if(ip instanceof Inet4Address) {
                bytes = ip.getAddress();
                return (bytes[0] & 255) + "." + (bytes[1] & 255) + "." + (bytes[2] & 255) + "." + (bytes[3] & 255);
            } else if(!(ip instanceof Inet6Address)) {
                throw new IllegalArgumentException("ip");
            } else {
                bytes = ip.getAddress();
                int[] hextets = new int[IPV6_PART_COUNT];

                for(int i = 0; i < hextets.length; ++i) {
                    hextets[i] = (bytes[2 * i] & 255) << 8 | bytes[2 * i + 1] & 255;
                }

                compressLongestRunOfZeroes(hextets);
                return hextetsToIPv6String(hextets);
            }
        }
    }

    private static void compressLongestRunOfZeroes(int[] hextets) {
        int bestRunStart = -1;
        int bestRunLength = -1;
        int runStart = -1;

        for(int i = 0; i < hextets.length + 1; ++i) {
            if(i < hextets.length && hextets[i] == 0) {
                if(runStart < 0) {
                    runStart = i;
                }
            } else if(runStart >= 0) {
                int runLength = i - runStart;
                if(runLength > bestRunLength) {
                    bestRunStart = runStart;
                    bestRunLength = runLength;
                }

                runStart = -1;
            }
        }

        if(bestRunLength >= 2) {
            Arrays.fill(hextets, bestRunStart, bestRunStart + bestRunLength, -1);
        }

    }

    private static String hextetsToIPv6String(int[] hextets) {
        StringBuilder buf = new StringBuilder(39);
        boolean lastWasNumber = false;

        for(int i = 0; i < hextets.length; ++i) {
            boolean thisIsNumber = hextets[i] >= 0;
            if(thisIsNumber) {
                if(lastWasNumber) {
                    buf.append(':');
                }

                buf.append(Integer.toHexString(hextets[i]));
            } else if(i == 0 || lastWasNumber) {
                buf.append("::");
            }

            lastWasNumber = thisIsNumber;
        }

        return buf.toString();
    }

    public static java.net.InetAddress forString(String ipString) {
        byte[] addr = ipStringToBytes(ipString);
        if(addr == null) {
            throw new IllegalArgumentException(String.format(Locale.ROOT, "\'%s\' is not an IP string literal.", new Object[]{ipString}));
        } else {
            return bytesToInetAddress(addr);
        }
    }

    private static java.net.InetAddress bytesToInetAddress(byte[] addr) {
        try {
            return java.net.InetAddress.getByAddress(addr);
        } catch (UnknownHostException var2) {
            throw new AssertionError(var2);
        }
    }
}