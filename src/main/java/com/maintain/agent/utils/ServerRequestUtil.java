package com.maintain.agent.utils;

import com.common.log.Log;
import com.maintain.agent.conf.Config;
import org.apache.http.HttpEntity;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class ServerRequestUtil {

    public static String maintainServerUrl;

    private static CloseableHttpClient httpClient;

    static {
        // 创建连接池管理器
        PoolingHttpClientConnectionManager cm = new PoolingHttpClientConnectionManager();
        // 设置最大连接数
        cm.setMaxTotal(200);
        // 设置每个主机的最大连接数
        cm.setDefaultMaxPerRoute(20);

        // 创建HttpClient实例
        httpClient = HttpClients.custom()
                .setConnectionManager(cm)
                .build();
    }

    public static CloseableHttpClient getHttpClient() {
        return httpClient;
    }

    public static String request(int flag, String param) {
        String url = "http://" + maintainServerUrl +"/maintain/request";
        CloseableHttpResponse resp = null;
        try {
            CloseableHttpClient client = getHttpClient();
            HttpPost httpPost = new HttpPost(url);
            List<BasicNameValuePair> list = new ArrayList<>();
            list.add(new BasicNameValuePair("flag", flag + ""));
            if (param != null) {
                list.add(new BasicNameValuePair("param", param));
            }
            httpPost.setEntity(new UrlEncodedFormEntity(list));
            resp = client.execute(httpPost);
            HttpEntity entity = resp.getEntity();
            return EntityUtils.toString(entity);
        } catch (Exception e) {
            Log.high.error("请求server:" + Config.getSystemConfig().getMaintainServerIp() + "，失败", e);
        } finally {
            if (resp != null) {
                try {
                    resp.close();
                } catch (IOException e) {
                    Log.high.error("关闭响应流出错", e);
                }
            }
        }
        return null;
    }

}
