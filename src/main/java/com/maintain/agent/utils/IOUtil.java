package com.maintain.agent.utils;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.io.PrintWriter;

/**
 * IO操作工具类
 * 提供文件读写和流操作的工具方法，包括：
 * 1. 输入流到字符串的转换
 * 2. 字符串到文件的写入
 * 3. 文件到字节数组的读取
 * 4. 文件到字符串的读取
 * 
 * 主要功能：
 * - 提供输入流到字符串的转换
 * - 支持字符串到文件的写入
 * - 提供文件到字节数组的读取
 * - 支持文件到字符串的读取
 * - 支持多种字符编码
 * - 提供文件追加和覆盖模式
 * - 自动创建不存在的文件
 * - 提供安全的流关闭处理
 * 
 * <AUTHOR>
 * @date 2016/3/8
 */
public final class IOUtil {
    private IOUtil() {
    }

    /**
     * 将输入流转换为字符串
     * 使用指定的字符编码读取输入流内容
     * 每行末尾添加\r\n换行符
     *
     * @param input 要转换的输入流
     * @param charset 字符编码
     * @return 转换后的字符串，如果输入流为null则返回null
     * @throws IOException 当读取输入流失败时抛出
     */
    public static String inputStreamToString(InputStream input, String charset) throws IOException {
        if (input == null) {
            return null;
        } else {
            InputStreamReader inputStreamReader = new InputStreamReader(input, charset);
            BufferedReader reader = new BufferedReader(inputStreamReader);
            String line = "";
            StringBuilder sb = new StringBuilder();
            while ((line = reader.readLine()) != null) {
                sb.append(line + "\r\n");
            }

            reader.close();
            inputStreamReader.close();

            return sb.toString();
        }
    }

    /**
     * 将字符串写入文件
     * 支持指定编码和追加模式
     * 如果文件不存在则创建新文件
     *
     * @param file 目标文件
     * @param content 要写入的内容
     * @param encode 文件编码
     * @param isAppend 是否追加模式，true表示追加，false表示覆盖
     * @throws IOException 当写入文件失败时抛出
     */
    public static void writeStringToFile(File file, String content, String encode, boolean isAppend) throws IOException {
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream outputStream = new FileOutputStream(file, isAppend);
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, encode);
        PrintWriter writer = new PrintWriter(outputStreamWriter);
        writer.write(content);
        writer.close();
        outputStreamWriter.close();
        outputStream.close();
    }

    /**
     * 将字符串写入文件
     * 支持指定编码和追加模式
     * 如果文件不存在则创建新文件
     *
     * @param filePath 目标文件路径
     * @param content 要写入的内容
     * @param encode 文件编码
     * @param isAppend 是否追加模式，true表示追加，false表示覆盖
     * @throws IOException 当写入文件失败时抛出
     */
    public static void writeStringToFile(String filePath, String content, String encode, boolean isAppend) throws IOException {
        File file = new File(filePath);
        writeStringToFile(file, content, encode, isAppend);
    }

    /**
     * 将文件读取为字节数组
     * 一次性读取整个文件内容
     *
     * @param filePath 文件路径
     * @return 文件内容的字节数组
     * @throws IOException 当读取文件失败时抛出
     */
    public static byte[] readFileToBinary(String filePath) throws IOException {
        File file = new File(filePath);
        return readFileToBinary(file);
    }

    /**
     * 将文件读取为字节数组
     * 一次性读取整个文件内容
     *
     * @param file 要读取的文件
     * @return 文件内容的字节数组
     * @throws IOException 当读取文件失败时抛出
     */
    public static byte[] readFileToBinary(File file) throws IOException {
        byte[] content = new byte[(int) file.length()];
        try (FileInputStream in = new FileInputStream(file)) {
            //读取文件内容到字节数组
            in.read(content);
            return content;
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 将文件读取为字符串
     * 使用指定的字符编码
     *
     * @param filePath 文件路径
     * @param encoding 字符编码
     * @return 文件内容的字符串
     * @throws IOException 当读取文件失败时抛出
     */
    public static String readFileToString(String filePath, String encoding) throws IOException {
        byte[] bytes = readFileToBinary(filePath);
        return new String(bytes, encoding);
    }

    /**
     * 将文件读取为字符串
     * 使用指定的字符编码
     *
     * @param file 要读取的文件
     * @param encoding 字符编码
     * @return 文件内容的字符串
     * @throws IOException 当读取文件失败时抛出
     */
    public static String readFileToString(File file, String encoding) throws IOException {
        byte[] bytes = readFileToBinary(file);
        return new String(bytes, encoding);
    }
}
