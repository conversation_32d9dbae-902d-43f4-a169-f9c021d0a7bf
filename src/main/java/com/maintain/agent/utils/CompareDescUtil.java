package com.maintain.agent.utils;

import java.io.File;
import java.util.Comparator;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2018-08-28
 */
public class CompareDescUtil implements Comparator<File> {

    /**
     * 1是降序
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(File o1, File o2) {
        long t1 = o1.lastModified();
        long t2 = o2.lastModified();
        if (t1 > t2) {
            return -1;
        } else if (t1 < t2) {
            return 1;
        } else {
            return 0;
        }

    }
}