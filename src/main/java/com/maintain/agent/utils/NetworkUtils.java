package com.maintain.agent.utils;

import com.maintain.agent.conf.Constants;

import java.net.SocketException;
import java.net.Inet4Address;
import java.net.Inet6Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Optional;

/**
 * 网络工具类
 * 提供网络接口和地址相关的工具方法，包括：
 * 1. 获取网络接口信息
 * 2. 获取不同类型的网络地址（回环地址、本地地址、全局地址等）
 * 3. 过滤IPv4和IPv6地址
 * 4. 网络接口的排序和过滤
 * 
 * 主要功能：
 * - 提供网络接口的获取和管理
 * - 支持IPv4和IPv6地址的处理
 * - 提供网络地址的排序和过滤
 * - 支持回环地址、本地地址和全局地址的获取
 * - 提供网络接口状态检查
 * - 支持地址重用设置
 * 
 * <AUTHOR>
 * @date 2019-01-23
 */
public abstract class NetworkUtils {
    /** 
     * 是否优先使用IPv6地址
     * @deprecated 使用系统属性java.net.preferIPv6Addresses替代
     */
    @Deprecated
    static final boolean PREFER_V6 = Boolean.parseBoolean(System.getProperty("java.net.preferIPv6Addresses", "false"));
    
    /** 
     * 是否支持IPv6
     * @deprecated 不再使用
     */
    @Deprecated
    public static final boolean SUPPORTS_V6;

    private NetworkUtils() {
    }

    /**
     * 计算网络地址的排序键值
     * 根据地址类型和偏好设置计算排序值
     *
     * @param address 网络地址
     * @param prefer_v6 是否优先IPv6
     * @return 排序键值
     */
    static int sortKey(InetAddress address, boolean prefer_v6) {
        int key = address.getAddress().length;
        if(prefer_v6) {
            key = -key;
        }

        if(address.isAnyLocalAddress()) {
            key += 5;
        }

        if(address.isMulticastAddress()) {
            key += 4;
        }

        if(address.isLoopbackAddress()) {
            key += 3;
        }

        if(address.isLinkLocalAddress()) {
            key += 2;
        }

        if(address.isSiteLocalAddress()) {
            ++key;
        }

        return key;
    }

    /**
     * 获取所有网络接口
     * 包括主接口和子接口
     *
     * @return 网络接口列表
     * @throws SocketException 当获取网络接口失败时抛出
     */
    public static List<NetworkInterface> getInterfaces() throws SocketException {
        ArrayList all = new ArrayList();
        addAllInterfaces(all, Collections.list(NetworkInterface.getNetworkInterfaces()));

        return all;
    }

    /**
     * 递归添加所有网络接口
     * 包括子接口
     *
     * @param target 目标列表
     * @param level 当前层级的接口列表
     */
    private static void addAllInterfaces(List<NetworkInterface> target, List<NetworkInterface> level) {
        if(!level.isEmpty()) {
            target.addAll(level);
            Iterator var2 = level.iterator();

            while(var2.hasNext()) {
                NetworkInterface intf = (NetworkInterface)var2.next();
                addAllInterfaces(target, Collections.list(intf.getSubInterfaces()));
            }
        }
    }

    /**
     * 获取默认的地址重用设置
     * Windows系统默认不重用地址
     *
     * @return 是否重用地址
     */
    public static boolean defaultReuseAddress() {
        return !Constants.WINDOWS;
    }

    /**
     * 获取所有回环地址
     * 只返回处于运行状态的接口的回环地址
     *
     * @return 回环地址数组
     * @throws SocketException 当获取地址失败时抛出
     * @throws IllegalArgumentException 当没有找到回环地址时抛出
     */
    static InetAddress[] getLoopbackAddresses() throws SocketException {
        ArrayList list = new ArrayList();
        Iterator var1 = getInterfaces().iterator();

        while(true) {
            NetworkInterface intf;
            do {
                if(!var1.hasNext()) {
                    if(list.isEmpty()) {
                        throw new IllegalArgumentException("No up-and-running loopback addresses found, got " + getInterfaces());
                    }

                    return (InetAddress[])list.toArray(new InetAddress[list.size()]);
                }

                intf = (NetworkInterface)var1.next();
            } while(!intf.isUp());

            Iterator var3 = Collections.list(intf.getInetAddresses()).iterator();

            while(var3.hasNext()) {
                InetAddress address = (InetAddress)var3.next();
                if(address.isLoopbackAddress()) {
                    list.add(address);
                }
            }
        }
    }

    /**
     * 获取所有本地站点地址
     * 只返回处于运行状态的接口的本地站点地址
     *
     * @return 本地站点地址数组
     * @throws SocketException 当获取地址失败时抛出
     * @throws IllegalArgumentException 当没有找到本地站点地址时抛出
     */
    static InetAddress[] getSiteLocalAddresses() throws SocketException {
        ArrayList list = new ArrayList();
        Iterator var1 = getInterfaces().iterator();

        while(true) {
            NetworkInterface intf;
            do {
                if(!var1.hasNext()) {
                    if(list.isEmpty()) {
                        throw new IllegalArgumentException("No up-and-running site-local (private) addresses found, got " + getInterfaces());
                    }

                    return (InetAddress[])list.toArray(new InetAddress[list.size()]);
                }

                intf = (NetworkInterface)var1.next();
            } while(!intf.isUp());

            Iterator var3 = Collections.list(intf.getInetAddresses()).iterator();

            while(var3.hasNext()) {
                InetAddress address = (InetAddress)var3.next();
                if(address.isSiteLocalAddress()) {
                    list.add(address);
                }
            }
        }
    }

    /**
     * 获取所有全局地址
     * 只返回处于运行状态的接口的全局地址
     *
     * @return 全局地址数组
     * @throws SocketException 当获取地址失败时抛出
     * @throws IllegalArgumentException 当没有找到全局地址时抛出
     */
    static InetAddress[] getGlobalAddresses() throws SocketException {
        ArrayList list = new ArrayList();
        Iterator var1 = getInterfaces().iterator();

        while(true) {
            NetworkInterface intf;
            do {
                if(!var1.hasNext()) {
                    if(list.isEmpty()) {
                        throw new IllegalArgumentException("No up-and-running global-scope (public) addresses found, got " + getInterfaces());
                    }

                    return (InetAddress[])list.toArray(new InetAddress[list.size()]);
                }

                intf = (NetworkInterface)var1.next();
            } while(!intf.isUp());

            Iterator var3 = Collections.list(intf.getInetAddresses()).iterator();

            while(var3.hasNext()) {
                InetAddress address = (InetAddress)var3.next();
                if(!address.isLoopbackAddress() && !address.isSiteLocalAddress() && !address.isLinkLocalAddress()) {
                    list.add(address);
                }
            }
        }
    }

    /**
     * 获取所有网络地址
     * 只返回处于运行状态的接口的地址
     *
     * @return 所有网络地址数组
     * @throws SocketException 当获取地址失败时抛出
     * @throws IllegalArgumentException 当没有找到任何地址时抛出
     */
    static InetAddress[] getAllAddresses() throws SocketException {
        ArrayList list = new ArrayList();
        Iterator var1 = getInterfaces().iterator();

        while(true) {
            NetworkInterface intf;
            do {
                if(!var1.hasNext()) {
                    if(list.isEmpty()) {
                        throw new IllegalArgumentException("No up-and-running addresses found, got " + getInterfaces());
                    }

                    return (InetAddress[])list.toArray(new InetAddress[list.size()]);
                }

                intf = (NetworkInterface)var1.next();
            } while(!intf.isUp());

            Iterator var3 = Collections.list(intf.getInetAddresses()).iterator();

            while(var3.hasNext()) {
                InetAddress address = (InetAddress)var3.next();
                list.add(address);
            }
        }
    }

    /**
     * 获取指定网络接口的所有地址
     *
     * @param name 网络接口名称
     * @return 该接口的所有地址数组
     * @throws SocketException 当获取地址失败时抛出
     * @throws IllegalArgumentException 当接口不存在或未运行或没有地址时抛出
     */
    static InetAddress[] getAddressesForInterface(String name) throws SocketException {
        Optional networkInterface = getInterfaces().stream().filter((netIf) -> {
            return name.equals(netIf.getName());
        }).findFirst();
        if(!networkInterface.isPresent()) {
            throw new IllegalArgumentException("No interface named \'" + name + "\' found, got " + getInterfaces());
        } else if(!((NetworkInterface)networkInterface.get()).isUp()) {
            throw new IllegalArgumentException("Interface \'" + name + "\' is not up and running");
        } else {
            ArrayList list = Collections.list(((NetworkInterface)networkInterface.get()).getInetAddresses());
            if(list.isEmpty()) {
                throw new IllegalArgumentException("Interface \'" + name + "\' has no internet addresses");
            } else {
                return (InetAddress[])list.toArray(new InetAddress[list.size()]);
            }
        }
    }

    /**
     * 过滤IPv4地址
     * 从地址数组中筛选出IPv4地址
     *
     * @param addresses 要过滤的地址数组
     * @return IPv4地址数组
     * @throws IllegalArgumentException 当没有找到IPv4地址时抛出
     */
    static InetAddress[] filterIPV4(InetAddress[] addresses) {
        ArrayList list = new ArrayList();
        InetAddress[] var2 = addresses;
        int var3 = addresses.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            InetAddress address = var2[var4];
            if(address instanceof Inet4Address) {
                list.add(address);
            }
        }

        if(list.isEmpty()) {
            throw new IllegalArgumentException("No ipv4 addresses found in " + Arrays.toString(addresses));
        } else {
            return (InetAddress[])list.toArray(new InetAddress[list.size()]);
        }
    }

    /**
     * 过滤IPv6地址
     * 从地址数组中筛选出IPv6地址
     *
     * @param addresses 要过滤的地址数组
     * @return IPv6地址数组
     * @throws IllegalArgumentException 当没有找到IPv6地址时抛出
     */
    static InetAddress[] filterIPV6(InetAddress[] addresses) {
        ArrayList list = new ArrayList();
        InetAddress[] var2 = addresses;
        int var3 = addresses.length;

        for(int var4 = 0; var4 < var3; ++var4) {
            InetAddress address = var2[var4];
            if(address instanceof Inet6Address) {
                list.add(address);
            }
        }

        if(list.isEmpty()) {
            throw new IllegalArgumentException("No ipv6 addresses found in " + Arrays.toString(addresses));
        } else {
            return (InetAddress[])list.toArray(new InetAddress[list.size()]);
        }
    }

    static {
        boolean v = false;

        try {
            Iterator misconfiguration = getInterfaces().iterator();

            label29:
            while(true) {
                while(true) {
                    if(!misconfiguration.hasNext()) {
                        break label29;
                    }

                    NetworkInterface nic = (NetworkInterface)misconfiguration.next();
                    Iterator var3 = Collections.list(nic.getInetAddresses()).iterator();

                    while(var3.hasNext()) {
                        InetAddress address = (InetAddress)var3.next();
                        if(address instanceof Inet6Address) {
                            v = true;
                            break;
                        }
                    }
                }
            }
        } catch (SocketException | SecurityException var5) {
            v = true;
        }

        SUPPORTS_V6 = v;
    }
}