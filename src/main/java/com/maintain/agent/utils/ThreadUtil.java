package com.maintain.agent.utils;

import com.common.log.Log;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 线程操作工具类
 * 提供多线程执行和管理的工具方法，包括：
 * 1. 多线程并发执行
 * 2. 线程池管理
 * 3. 线程执行状态监控
 * 
 * 主要功能：
 * - 提供固定大小的线程池执行任务
 * - 支持批量提交线程任务
 * - 提供任务执行超时控制
 * - 使用ThreadPoolExecutor实现线程池管理
 * - 支持线程执行异常处理和日志记录
 * 
 * <AUTHOR>
 * @date 2017/6/16
 */
public final class ThreadUtil {
    private ThreadUtil() {
    }

    /**
     * 并发执行多个线程任务
     * 使用固定大小的线程池执行任务
     * 等待所有任务执行完成或超时
     * 超时时间设置为1分钟
     *
     * @param threads 要执行的线程任务列表
     * @param maxThreadNum 线程池大小，同时执行的最大线程数
     * @throws InterruptedException 当等待线程执行完成时被中断抛出
     */
    public static void executeMultiThread(java.util.List<Runnable> threads, int maxThreadNum) {
        if (threads.isEmpty()) {
            return;
        }

        ThreadFactory namedThreadFactory = Executors.defaultThreadFactory();
        ExecutorService executorService = new ThreadPoolExecutor(maxThreadNum, maxThreadNum,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>(),
                namedThreadFactory,
                new ThreadPoolExecutor.AbortPolicy());
        for (int i = 0; i < threads.size(); i++) {
            executorService.submit(threads.get(i));
        }
        //退出使用线程池
        executorService.shutdown();
        try {
            executorService.awaitTermination(1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            Log.high.error("executeMultiThread InterruptedException happened", e);
            Thread.currentThread().interrupt();
        }
    }
}
