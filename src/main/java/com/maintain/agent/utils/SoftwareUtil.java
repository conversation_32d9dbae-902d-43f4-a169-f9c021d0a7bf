package com.maintain.agent.utils;

import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.struct.SoftwareInfo;
import com.maintain.agent.enums.SoftwareOperateEnum;
import com.maintain.agent.server.SoftwareMonitor;
import com.maintain.agent.software.BaseSoftware;
import com.maintain.agent.software.SoftwareList;
import com.common.log.Log;
import com.common.util.SigarUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.Set;
import java.util.StringJoiner;

public class SoftwareUtil {

    public static SoftwareList softwareManage = new SoftwareList();

    public static SoftwareList getSoftwareManage() {
        try {
            final SoftwareList baseSoftwares;
            if (ProcessUtil.isWindows()) {
                baseSoftwares = new ObjectMapper().readValue(new File("./conf/software-windows.json"), SoftwareList.class);
            } else {
                baseSoftwares = new ObjectMapper().readValue(new File("./conf/software-linux.json"), SoftwareList.class);
            }
            final StringJoiner stringJoiner = new StringJoiner(",", "特殊守护程序[", "]");
            baseSoftwares.forEach(b -> stringJoiner.add(b.toString()));
            Log.low.info(stringJoiner.toString());
            return baseSoftwares;
        } catch (IOException e) {
            Log.high.error("初始化特殊程序守护失败, " + e.getMessage(), e);
        }
        return new SoftwareList();
    }

    /**
     *  直接开启程序
     * @param software
     * @return
     */
    public static Integer openSoftware(String software){
        SoftwareMonitor instance = SoftwareMonitor.INSTANCE;
        Set<Integer> pidSet;
        SoftwareInfo softwareInfo = instance.getSoftwareInfo(software);
        if (softwareInfo == null) {
            synchronized (SoftwareMonitor.INSTANCE) {
                if (instance.getSoftwareInfo(software) == null) {
                    SoftwareMonitor.INSTANCE.initSoftwareInfo();
                }
            }
            softwareInfo = instance.getSoftwareInfo(software);
            if (softwareInfo == null) {
                Log.low.error("要开启的程序不存在");
                return null;
            }
        }
        if (!softwareManage.isEmpty()) {
            for (BaseSoftware soft : softwareManage) {
                if (software.equals(soft.getName())) {
                    soft.openSoftware(softwareInfo.getRealDir());
                    Log.low.info("5");
                    pidSet = soft.getPidSet();
                    if (pidSet.isEmpty()) {
                        return null;
                    } else {
                        return pidSet.iterator().next();
                    }
                }
            }
        }
        String startCommand;
        if (ProcessUtil.isWindows()) {
            startCommand = " cd /d \"" + softwareInfo.getRealDir() + "\\deploy\" && start_background.vbs";
            final File file = new File(softwareInfo.getRealDir() + "\\deploy\\start_background.vbs");
            if(!file.exists()){
                throw new RuntimeException("start_background.vbs脚本不存在");
            }
                instance.clearSoftwareStartInfo(software);
        } else {
            //linux
            if(!new File(softwareInfo.getRealDir() + "/deploy/start.sh").exists()){
                throw new RuntimeException("start.sh 脚本不存在");
            }
            startCommand = "cd " + softwareInfo.getRealDir() + "/deploy && chmod 777 start.sh && nohup sh start.sh >/dev/null &";
                instance.clearSoftwareStartInfo(software);
        }
        ProcessUtil.execLocalCommand(startCommand);
        return getPid(software);
    }

    /**
     *  安全开启程序
     * @param software
     * @return
     */
    public static Integer safeOpenSoftware(String software) {
        SoftwareMonitor instance = SoftwareMonitor.INSTANCE;
        Set<Integer> pidSet;
        SoftwareInfo softwareInfo = instance.getSoftwareInfo(software);
        if (softwareInfo == null) {
            synchronized (SoftwareMonitor.INSTANCE) {
                if (instance.getSoftwareInfo(software) == null) {
                    SoftwareMonitor.INSTANCE.initSoftwareInfo();
                }
            }
            softwareInfo = instance.getSoftwareInfo(software);
            if (softwareInfo == null) {
                Log.low.error("要开启的程序不存在");
                return null;
            }
        }
        if (!softwareManage.isEmpty()) {
            for (BaseSoftware soft : softwareManage) {
                if (software.equals(soft.getName())) {
                    soft.openSoftware(softwareInfo.getRealDir());
                    pidSet = soft.getPidSet();
                    if (pidSet.isEmpty()) {
                        return null;
                    } else {
                        return pidSet.iterator().next();
                    }
                }
            }
        }
        String startCommand;
        if (ProcessUtil.isWindows()) {
            startCommand = " cd /d \"" + softwareInfo.getRealDir() + "\\deploy\" && start_background.vbs";
            final File file = new File(softwareInfo.getRealDir() + "\\deploy\\start_background.vbs");
            if(!file.exists()){
                throw new RuntimeException("start_background.vbs脚本不存在");
            }
            pidSet = instance.getWindowsSoftwarePid(instance.getWindowsCommandResultMap(), software);
            if(Objects.isNull(pidSet) || pidSet.isEmpty()){
                instance.clearSoftwareStartInfo(software);
                pidSet = instance.getWindowsSoftwarePid(instance.getWindowsCommandResultMap(), software);
            }
        } else {
            //linux
            if(!new File(softwareInfo.getRealDir() + "/deploy/start.sh").exists()){
                throw new RuntimeException("start.sh 脚本不存在");
            }
            startCommand = "cd " + softwareInfo.getRealDir() + "/deploy && chmod 777 start.sh && nohup sh start.sh >/dev/null &";
            pidSet = instance.getLinuxSoftwarePid(software);
            if(Objects.isNull(pidSet) || pidSet.isEmpty()){
                instance.clearSoftwareStartInfo(software);
                pidSet = instance.getLinuxSoftwarePid(software);
            }
        }
        if (pidSet.isEmpty()) {
            ProcessUtil.execLocalCommand(startCommand);
            return getPid(software);
        } else {
            if (instance.softwareHeartBeatTimeout(software)) {
                closeSoftware(software, pidSet.iterator().next());
                ProcessUtil.execLocalCommand(startCommand);
                return getPid(software);
            } else {
                return getPid(software);
            }
        }
    }

    public static Integer closeSoftware(String software, Integer pid) {
        SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(software);
        if (!softwareManage.isEmpty()) {
            for (BaseSoftware soft : softwareManage) {
                if (software.equals(soft.getName())) {
                    soft.closeSoftware(softwareInfo.getRealDir());
                    SoftwareMonitor.INSTANCE.clearSoftwareStartInfo(software);
                    return pid;
                }
            }
        }
        String killCommand;
        if (ProcessUtil.isWindows()) {
            killCommand = "taskkill /f /pid ";
        } else {
            killCommand = "kill -9 ";
        }
        if (pid != null && !pid.equals(-1)) {
            try {
                SigarUtil.sigar.kill(pid, 9);
            } catch (Exception e) {
                Log.high.debug(e.getMessage(), e);
            }
            ProcessUtil.execLocalCommand(killCommand + pid);
        }
        Set<Integer> pidSet;
        Integer realPid = null;
        if (ProcessUtil.isWindows()) {
            pidSet = SoftwareMonitor.INSTANCE.getWindowsSoftwarePid(SoftwareMonitor.INSTANCE.getWindowsCommandResultMap(), software);
            if(Objects.isNull(pidSet) || pidSet.isEmpty()){
                SoftwareMonitor.INSTANCE.clearSoftwareStartInfo(software);
                pidSet = SoftwareMonitor.INSTANCE.getWindowsSoftwarePid(SoftwareMonitor.INSTANCE.getWindowsCommandResultMap(), software);
            }
        } else {
            pidSet = SoftwareMonitor.INSTANCE.getLinuxSoftwarePid(software);
            if(Objects.isNull(pidSet) || pidSet.isEmpty()){
                SoftwareMonitor.INSTANCE.clearSoftwareStartInfo(software);
                pidSet = SoftwareMonitor.INSTANCE.getLinuxSoftwarePid(software);
            }
        }
        if (!pidSet.isEmpty()) {
            for (Integer integer : pidSet) {
                try {
                    SigarUtil.sigar.kill(integer, 9);
                    ProcessUtil.execLocalCommand(killCommand + integer);
                    realPid = integer;
                } catch (Exception e) {
                    Log.high.error(e.getMessage(), e);
                }

            }
        }
        softwareInfo.setPid(-1);
        SoftwareMonitor.INSTANCE.clearSoftwareHeartBeatTime(software);
        SoftwareMonitor.INSTANCE.clearSoftwareStartInfo(software);
        return realPid == null ? pid : realPid;
    }

    private static Integer getPid(String software) {
        if (ProcessUtil.isWindows()) {
            final Set<Integer> windowsSoftwarePid = SoftwareMonitor.INSTANCE.getWindowsSoftwarePid(SoftwareMonitor.INSTANCE.getWindowsCommandResultMap(), software);
            if (windowsSoftwarePid.isEmpty()) {
                return null;
            } else {
                return windowsSoftwarePid.iterator().next();
            }
        } else {
            final Set<Integer> linuxSoftwarePid = SoftwareMonitor.INSTANCE.getLinuxSoftwarePid(software);
            if (linuxSoftwarePid.isEmpty()) {
                return null;
            } else {
                return linuxSoftwarePid.iterator().next();
            }
        }
    }

    /**
     * 程序启动脚本是否存在
     *
     * @param soft
     * @param osName
     * @return
     */
    public static boolean startScriptExists(SoftwareInfo soft, String osName) {
        try {
            String dir = soft.getRealDir() + File.separator + soft.getScriptPath();
            String script = soft.getScript();
            String filePath;
            Integer operateType = soft.getOperateType();
            if (soft.getConfig()) {
                if (StringUtils.isEmpty(soft.getScriptPath())) {
                    //用命令启动程序，程序本身作为系统服务
                    if (script.startsWith("service")) {
                        String command = "service " + script.split("\\s+")[1] + " status";
                        String result = ProcessUtil.execute(command, false);

                        if (result != null && result.contains("unrecognized service")) {
                            return false;
                        }
                        return true;
                    } else if (script.startsWith("systemctl")) {
                        String command = "systemctl status " + script.split("\\s+")[2];
                        String result = ProcessUtil.execute(command, false);

                        if (result != null && result.contains("not-found")) {
                            return false;
                        }
                        return true;
                    }
                }
                //手动配置的程序，script是启动命令，而非启动脚本
                String startScript = "";
                for (String item : script.split("\\s+")) {
                    if (item.endsWith(".sh") || item.endsWith("vbs") || item.endsWith("bat")) {
                        startScript = item;
                        break;
                    }
                }
                if ("".equals(startScript)) {
                    Log.low.info("invalid start command:" + script);
                    return false;
                }
                filePath = dir + File.separator + startScript;
            } else {
                if (osName.contains(Constants.OS_WINDOWS)) {
                    filePath = soft.getRealDir() + "\\deploy\\start_background.vbs";
                } else {
                    filePath = soft.getRealDir() + "/deploy/start.sh";
                }
            }
            Log.low.info("check file path:" + filePath);
            File file = new File(filePath);
            if ((operateType.equals(SoftwareOperateEnum.OPEN.getId())
                    || operateType.equals(SoftwareOperateEnum.RELOAD.getId())) && !file.exists()) {
                return false;
            }
            return true;
        } catch (Exception e) {
            Log.high.error("检查程序启动脚本是否存在异常", e);
        }
        return true;
    }
}
