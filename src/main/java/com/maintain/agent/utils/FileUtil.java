package com.maintain.agent.utils;

import com.common.log.Log;
import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.io.FileUtils;

import java.io.*;
import java.util.Map;

/**
 * 文件操作工具类
 * 提供文件的基本操作功能，包括：
 * 1. 文件的创建、删除和修改
 * 2. 目录的创建、删除和大小计算
 * 3. 文件内容的读写操作
 * 4. JSON文件的更新
 * 5. 流资源的关闭
 * 
 * 主要功能：
 * - 提供文件基本操作（创建、删除、修改）
 * - 支持目录操作和大小计算
 * - 提供文件内容读写功能
 * - 支持JSON文件更新和解析
 * - 提供安全的流资源关闭方法
 * - 支持文件操作的重试机制
 * - 提供递归目录操作
 * 
 * <AUTHOR>
 * @date 2017/4/6
 */
public final class FileUtil {
    private FileUtil() {
    }

    /**
     * 删除指定文件
     * 支持重试机制，当文件被占用时可以多次尝试删除
     *
     * @param file  要删除的文件对象
     * @param retry 是否启用重试机制，true表示启用，false表示只尝试一次
     */
    public static void deleteFile(File file, boolean retry) {
        //do once
        if (!retry) {
            file.delete();
            return;
        }
        //retry
        while (!file.delete()) {
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Log.low.error("FileUtil deleting [file:" + file.getPath() + "]", e);
            }
        }
    }

    /**
     * 创建新文件
     * 支持重试机制，当文件创建失败时可以多次尝试
     *
     * @param filePath 要创建的文件路径
     * @param retry    是否启用重试机制，true表示启用，false表示只尝试一次
     * @return 创建成功的文件对象，如果创建失败则返回null
     */
    public static File createFile(String filePath, boolean retry) {
        File file = new File(filePath);
        if (file.exists()) {
            return file;
        }
        //do once
        if (!retry) {
            try {
                if (file.createNewFile()) {
                    return file;
                }
                return null;
            } catch (IOException e) {
                Log.low.error("FileUtil createFile [file:" + filePath + "]", e);
                return null;
            }
        }
        //retry
        try {
            while (!file.createNewFile()) {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    Log.low.error("FileUtil creating [file:" + filePath + "]", e);
                }
            }
            return file;
        } catch (IOException e) {
            Log.low.error("FileUtil createFile [file:" + filePath + "]", e);
            return null;
        }
    }

    /**
     * 计算目录的总大小
     * 递归计算目录下所有文件的大小总和
     *
     * @param dir 要计算大小的目录
     * @return 目录的总大小（字节数）
     * @throws FileNotFoundException 当目录不存在时抛出
     */
    public static long getDirSize(File dir) throws FileNotFoundException {
        if (!dir.exists()) {
            throw new FileNotFoundException();
        }
        if (dir.isFile()) {
            return dir.length();
        }

        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return 0;
        }
        long size = 0;
        for (File file : files) {
            size += getDirSize(file);
        }
        return size;
    }

    /**
     * 安全关闭输入流
     * 忽略关闭过程中可能发生的异常
     *
     * @param stream 要关闭的输入流
     */
    public static void closeInputStream(InputStream stream) {
        try {
            if (stream != null) {
                stream.close();
            }
        } catch (IOException e) {
            Log.high.error(e);
        }
    }

    /**
     * 安全关闭输出流
     * 忽略关闭过程中可能发生的异常
     *
     * @param stream 要关闭的输出流
     */
    public static void closeOutputStream(OutputStream stream) {
        try {
            if (stream != null) {
                stream.close();
            }
        } catch (IOException e) {
            Log.high.error(e);
        }
    }

    /**
     * 安全关闭Reader
     * 忽略关闭过程中可能发生的异常
     *
     * @param reader 要关闭的Reader
     */
    public static void closeReader(Reader reader) {
        try {
            if (reader != null) {
                reader.close();
            }
        } catch (IOException e) {
            Log.high.error(e);
        }
    }

    /**
     * 安全关闭Writer
     * 忽略关闭过程中可能发生的异常
     *
     * @param writer 要关闭的Writer
     */
    public static void closeWriter(Writer writer) {
        try {
            if (writer != null) {
                writer.close();
            }
        } catch (IOException e) {
            Log.high.error(e);
        }
    }

    /**
     * 递归删除目录及其内容
     * 会删除目录下的所有文件和子目录
     *
     * @param dir 要删除的目录
     * @return 删除是否成功
     */
    public static boolean deleteDir(File dir) {
        if (!dir.exists()) {
            return true;
        }
        if (dir.isFile()) {
            return dir.delete();
        }
        File[] files = dir.listFiles();
        if (files != null && files.length > 0) {
            for (File file : files) {
                if (file.isDirectory()) {
                    if (!deleteDir(file)) {
                        return false;
                    }
                } else {
                    if (!file.delete()) {
                        return false;
                    }
                }
            }
        }
        return dir.delete();
    }

    /**
     * 更新JSON文件内容
     * 支持更新嵌套的JSON结构
     *
     * @param filePath JSON文件路径
     * @param map 要更新的键值对
     * @return 更新是否成功
     */
    public static boolean updateJson(String filePath, Map<String, Object> map) {
        File file = new File(filePath);
        if (!file.exists()) {
            Log.high.error("Dir is not a file," + filePath);
            return false;
        }
        Map<String, Object> config = null;
        try {
            JsonFactory factory = new JsonFactory();
            factory.enable(JsonParser.Feature.ALLOW_COMMENTS);
            ObjectMapper objectMapper = new ObjectMapper(factory);
            // 忽略JSON字符串中存在的但Java对象实际没有的属性
            objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false);
            config = objectMapper.readValue(file, Map.class);
            for (Map.Entry<String, Object> s : map.entrySet()) {
                updateMap(config, s.getKey(), map.get(s.getKey()));
            }
            new ObjectMapper().writerWithDefaultPrettyPrinter().writeValue(file, config);
            return true;
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
            return false;
        }
    }

    /**
     * 递归更新Map中的值
     * 支持更新嵌套Map中的值
     *
     * @param map 要更新的Map
     * @param key 要更新的键
     * @param value 新的值
     */
    private static void updateMap(Map<String, Object> map, String key, Object value) {
        if (map.containsKey(key)) {
            map.put(key, value);
        } else {
            for (Object o : map.values()) {
                if (o.getClass().toString().toLowerCase().contains("map")) {
                    updateMap((Map<String, Object>) o, key, value);
                }
            }
        }
    }

    /**
     * 更新键值对格式文件的内容
     * 支持同时更新多个键值对
     *
     * @param filePath 文件路径
     * @param map 要更新的键值对Map
     * @return 更新是否成功
     */
    public static boolean updateKeyValue(String filePath, Map<String, String> map) {
        File file = new File(filePath);
        StringBuilder sb = new StringBuilder();

        try (FileInputStream fileInputStream = new FileInputStream(file);
             InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");
             BufferedReader reader = new BufferedReader(inputStreamReader)) {

            String line = "";
            while ((line = reader.readLine()) != null) {
                if (!line.startsWith("#") && line.contains("=")) {
                    String[] content = line.split("=");
                    if (map.containsKey(content[0])) {
                        line = line.replace(content[1], map.get(content[0]));
                    }
                }
                sb.append(line);
                sb.append("\r\n");
            }
        } catch (IOException e) {
            return false;
        }

        try (OutputStream outputStream = new FileOutputStream(file);
             PrintWriter writer = new PrintWriter(outputStream)) {
            writer.write(sb.toString());
            return true;
        } catch (IOException e) {
            return false;
        }
    }

    /**
     * 将字符串写入文件
     * 支持指定编码和追加模式
     *
     * @param file 目标文件
     * @param content 要写入的内容
     * @param encode 文件编码
     * @param isAppend 是否追加模式
     * @throws IOException 写入失败时抛出
     */
    public static void writeStringToFile(File file, String content, String encode, boolean isAppend) throws IOException {
        if (!file.exists()) {
            file.createNewFile();
        }
        FileOutputStream outputStream = new FileOutputStream(file, isAppend);
        OutputStreamWriter outputStreamWriter = new OutputStreamWriter(outputStream, encode);
        PrintWriter writer = new PrintWriter(outputStreamWriter);
        writer.write(content);
        writer.flush();
        writer.close();
        outputStreamWriter.close();
        outputStream.close();
    }

    /**
     * 将字符串写入文件
     * 支持指定编码和追加模式
     *
     * @param filePath 目标文件路径
     * @param content 要写入的内容
     * @param encode 文件编码
     * @param isAppend 是否追加模式
     * @throws IOException 写入失败时抛出
     */
    public static void writeStringToFile(String filePath, String content, String encode, boolean isAppend) throws IOException {
        File file = new File(filePath);
        writeStringToFile(file, content, encode, isAppend);
    }

    /**
     * 读取输入流内容
     * 支持指定编码
     *
     * @param is 输入流
     * @param code 编码
     * @return 读取的内容
     * @throws IOException 读取失败时抛出
     */
    public static String readInputStream(InputStream is, String code) throws IOException {
        try{
            if (is == null) {
                return null;
            }
            BufferedReader reader = new BufferedReader(new InputStreamReader(is, code));
            StringBuilder sb = new StringBuilder();
            String str;
            while ((str = reader.readLine()) != null) {
                sb.append(str).append("\n");
            }
            return sb.toString();
        }finally {
            if(is != null){
                is.close();
            }
        }
    }

    /**
     * 获取文件大小
     *
     * @param file 目标文件
     * @return 文件大小（字节数）
     */
    public static long sizeOf(File file) {
        if(!file.exists()) {
            return 0L;
        } else {
            return file.isDirectory()?sizeOfDirectory(file):file.length();
        }
    }

    /**
     * 获取目录大小
     * 递归计算目录下所有文件的大小总和
     *
     * @param directory 目标目录
     * @return 目录大小（字节数）
     */
    public static long sizeOfDirectory(File directory) {
        checkDirectory(directory);
        File[] files = directory.listFiles();
        if(files == null) {
            return 0L;
        } else {
            long size = 0L;
            File[] arr$ = files;
            int len$ = files.length;

            for(int i$ = 0; i$ < len$; ++i$) {
                File file = arr$[i$];

                try {
                    if(!FileUtils.isSymlink(file)) {
                        size += sizeOf(file);
                        if(size < 0L) {
                            break;
                        }
                    }
                } catch (IOException var9) {
                    ;
                }
            }

            return size;
        }
    }

    /**
     * 检查目录是否有效
     * 验证目录是否存在且可访问
     *
     * @param directory 要检查的目录
     * @throws IllegalArgumentException 当目录无效时抛出
     */
    private static void checkDirectory(File directory) {
        if(!directory.exists()) {
            throw new IllegalArgumentException(directory + " does not exist");
        } else if(!directory.isDirectory()) {
            throw new IllegalArgumentException(directory + " is not a directory");
        }
    }

    /**
     * 获取目录中所有文件的总大小
     * 递归计算所有文件的大小总和
     *
     * @param file 目标目录
     * @return 所有文件的总大小（字节数）
     */
    public static long getTotalSizeOfFilesInDir(final File file) {
        if (file.isFile())
            return file.length();
        final File[] children = file.listFiles();
        long total = 0;
        if (children != null)
            for (final File child : children)
                total += getTotalSizeOfFilesInDir(child);
        return total;
    }

    /**
     * 获取文件长度
     *
     * @param file 目标文件
     * @return 文件长度（字节数）
     */
    public static long getFileLength(File file) {
        long fileLength = 0;
        if (file.exists() && file.isFile()) {
            fileLength = file.length();
        }
        return fileLength;
    }

}
