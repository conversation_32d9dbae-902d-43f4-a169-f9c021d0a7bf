package com.maintain.agent.utils;

import com.common.log.Log;
import com.maintain.agent.business.service.HardInfo;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.struct.ProcStruct;
import com.maintain.agent.entity.struct.ProcStructRaw;
import org.apache.commons.lang.StringUtils;
import org.hyperic.sigar.ProcExe;
import org.hyperic.sigar.ProcState;
import org.hyperic.sigar.ProcTime;
import org.hyperic.sigar.SigarException;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2018-01-30
 */
public class ProcessUtil {


    private static final String osName = System.getProperty("os.name").toLowerCase();

    public static boolean isWindows() {
        if (osName.contains("linux")) {
            return false;
        } else if (osName.contains("windows")) {
            return true;
        }
        return false;
    }

   /* public static boolean isLinux(){
        return !isWindows();
    }*/

    /**
     * 根据关键字得到进程的pid
     *
     * @param keys 进程命令行中的关键字
     * @return 返回进程的pid
     */
    public static List<Long> getPidByParam(List<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return new ArrayList<>();
        }
        List<ProcStructRaw> list = getAllProcs();
        if (list == null) {
            return null;
        }

        List<Long> result = new ArrayList<>();

        for (String key : keys) {
            for (ProcStructRaw proc : list) {
                String cmd = proc.getCmd();
                String pid = proc.getPid();

                if (cmd.contains(key)) {
                    result.add(Long.parseLong(pid));
                    break;
                }
            }
        }
        return result;
    }

    /**
     * 杀掉指定PID对应的进程
     *
     * @param pid 进程PID
     * @return 是否成功杀死指定进程
     */
    public static boolean killProcess(long pid) {
        try {
            ProcState ps = HardInfo.getSigar().getProcState(pid);
            String procName = ps.getName();
            if (procName == null || procName.isEmpty()) {
                return false;
            }
            HardInfo.getSigar().kill(pid, 9);
            return true;
        } catch (SigarException e) {
            Log.high.error("killProc [pid:" + pid + "] error", e);
            return false;
        }
    }

    /**
     * 杀掉指定PID对应的进程
     *
     * @param pid 进程PID
     * @return 是否成功杀死指定进程
     */
    public static boolean killProcess(String pid) {
        return killProcess(Long.parseLong(pid));
    }

    /**
     * 获取所有进程的信息
     *
     * @return 所有进程的信息
     */
    public static List<ProcStructRaw> getAllProcs() {
        long[] pidList;
        try {
            pidList = HardInfo.getSigar().getProcList();
        } catch (SigarException e) {
            Log.high.error("getAllProcs getProcList error", e);
            return null;
        }
        if (pidList == null) {
            Log.high.error("getAllProcs [pidList:null] error");
            return null;
        }

        List<ProcStructRaw> allProcs = new LinkedList<>();
        for (long pid : pidList) {
            String[] namePpid = getProcState(pid);
            if (namePpid == null || namePpid.length != 2) {
                continue;
            }
            String name = namePpid[0];

            String pp = getParentProcs(pid);
            if (pp == null) {
                continue;
            }

            String time = getProcStartTime(pid);
            if (time == null) {
                continue;
            }

            String procArgs = getProcArgs(pid);
            if (procArgs == null || procArgs.isEmpty()) {
                continue;
            }
            String[] nameCwd = getProcExe(pid);
            if (nameCwd == null || nameCwd.length != 2) {
                continue;
            }
            String cmd = procArgs;
            String cwd = getWorkDir(nameCwd);

            ProcStructRaw struct = new ProcStructRaw();
            struct.setPid(String.valueOf(pid));
            struct.setName(name);
            struct.setPp(pp);
            struct.setTime(time);
            struct.setCmd(cmd);
            struct.setCwd(cwd);
            allProcs.add(struct);
        }
        return allProcs;
    }

    /**
     * 获取所有父进程的进程名和进程PID（除系统进程外）
     *
     * @param pid 进程PID
     * @return 所有父进程的进程名和进程PID
     */
    private static String getParentProcs(long pid) {
        if (pid <= 1) {
            return null;
        }

        String[] namePpid = getProcState(pid);
        if (namePpid == null) {
            return null;
        }
        String name = namePpid[0];
        Set<String> sysProcNames = Config.getSystemConfig().getSysProcNames();
        if (sysProcNames.contains(name)) {
            return "";
        }
        long ppid = Long.parseLong(namePpid[1]);
        String str = getParentProcs(ppid);
        if (str == null) {
            return "";
        }
        String pp = name + "(" + pid + ")->" + str;
        if (pp.endsWith("->")) {
            pp = pp.substring(0, pp.length() - 2);
        }
        return pp;
    }

    /**
     * 获取进程的启动时间（字符串形式）
     *
     * @param pid 进程PID
     * @return 进程的启动时间
     */
    private static String getProcStartTime(long pid) {
        ProcTime procTime;
        try {
            procTime = HardInfo.getSigar().getProcTime(pid);
        } catch (SigarException e) {
            //内部调用，某些PID可能会有异常，无需记录到日志
            return null;
        }
        long startTime = procTime.getStartTime();
        if (startTime <= 0) {
            return null;
        }
        return DateUtil.formatDate(startTime, Constants.YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取进程的启动参数
     *
     * @param pid 进程PID
     * @return 启动参数拼接后的字符串
     */
    public static String getProcArgs(long pid) {
        String[] procArgs;
        try {
            procArgs = HardInfo.getSigar().getProcArgs(pid);
        } catch (SigarException e) {
            //内部调用，某些PID可能会有异常，无需记录到日志
            return null;
        }
        StringBuilder builder = new StringBuilder();
        for (String procArg : procArgs) {
            builder.append(" ").append(procArg);
        }
        return builder.toString();
    }

    /**
     * 获取进程的执行信息
     *
     * @param pid 进程PID
     * @return 进程名和启动命令
     */
    public static String[] getProcExe(long pid) {
        ProcExe procExe;
        try {
            procExe = HardInfo.getSigar().getProcExe(pid);
        } catch (SigarException e) {
            //内部调用，某些PID可能会有异常，无需记录到日志
            return null;
        }
        return new String[]{procExe.getName(), procExe.getCwd()};
    }

    /**
     * 获取进程的工作路径，按照运维系统的需求进行了修正
     * <p>
     * name="C:\Program Files\Java\jdk1.8.0_102\bin\java.exe", cwd="D:\dist\StaticScanCenter";
     * 返回"D:\dist\StaticScanCenter"
     * <p>
     * name="C:\Windows\SysWOW64\cmd.exe", cwd="C:\Windows"; 返回"C:\Windows\SysWOW64"
     * name="D:\dist\PeThreatScan-1\PEIcon_AllPlatform.exe", cwd="C:\Windows"; 返回"D:\dist\PeThreatScan-1"
     *
     * @param nameCwd 进程的名称和启动命令
     * @return 进程的工作路径
     */
    public static String getWorkDir(String[] nameCwd) {
        String name = nameCwd[0];
        String cwd = nameCwd[1];
        if (name.endsWith("java.exe")) {
            return cwd;
        }
        if (name.endsWith(".exe")) {
            int index = name.lastIndexOf(File.separator);
            if (index != -1) {
                return name.substring(0, index);
            } else {
                return cwd;
            }
        }
        return cwd;
    }

    /**
     * 获取进程统计信息
     *
     * @param pid 进程PID
     * @return 进程名和进程PPID
     */
    private static String[] getProcState(long pid) {
        ProcState procState;
        try {
            procState = HardInfo.getSigar().getProcState(pid);
        } catch (SigarException e) {
            //内部调用，Windows中某些PID确实会有异常，无需记录到日志
            return null;
        }
        return new String[]{procState.getName(), String.valueOf(procState.getPpid())};
    }

    /**
     * 过滤系统进程
     *
     * @param procStructs 过滤范围
     * @return 过滤后的进程
     */
    public static List<ProcStruct> filterSysProcs(List<ProcStructRaw> procStructs) {
        Set<String> sysProcNames = Config.getSystemConfig().getSysProcNames();
        List<ProcStruct> procsFiltered = new LinkedList<>();
        for (ProcStructRaw struct : procStructs) {
            //使用进程名过滤系统进程
            if (sysProcNames.contains(struct.getName())) {
                continue;
            }
            procsFiltered.add(copyToProcStruct(struct));
        }
        return procsFiltered;
    }


    /**
     * 拷贝部分进程属性形成一个进程属性结构体
     *
     * @param procStructRaw 进程属性结构体原始形式
     * @return 进程属性结构体
     */
    private static ProcStruct copyToProcStruct(ProcStructRaw procStructRaw) {
        ProcStruct procStruct = new ProcStruct();
        procStruct.setPid(procStructRaw.getPid());
        procStruct.setName(procStructRaw.getName());
        procStruct.setPp(procStructRaw.getPp());
        procStruct.setTime(procStructRaw.getTime());
        procStruct.setCmd(procStructRaw.getCmd());
        return procStruct;
    }

    public static void executeLinux(boolean autoLog, String... commands) {
        if (commands != null) {
            for (String command : commands) {
                try {
                    TimeUnit.SECONDS.sleep(3);
                } catch (InterruptedException e) {
                    Log.high.error(e);
                }
                execute(command, autoLog);
            }
        }
    }

    public static void executeWindows(String... commands) {
        if (commands != null) {
            for (String command : commands) {
                try {
                    TimeUnit.SECONDS.sleep(3);
                } catch (InterruptedException e) {
                    Log.high.error(e);
                }
                executeWindows(command);
            }
        }
    }

    public static void execLocalCommand(String command) {
        if (StringUtils.isEmpty(command)) {
            return;
        }
        //Log.low.debug("execLocalCommand：" + command);
        Runtime runtime = Runtime.getRuntime();
        try {
            if (osName.contains("linux")) {
                runtime.exec(new String[]{"/bin/bash", "-c", command});
            } else {
                runtime.exec(new String[]{"cmd", "/c", command});
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    public static Set<Integer> getPorts() {
        final HashSet<Integer> integers = new HashSet<>();
        final Pattern compile = Pattern.compile("[\\[\\]\\w\\\\.:]+:\\d+");
        if (ProcessUtil.isWindows()) {
            try {
                final String[] strings = ExecUtil.execCmdOnWindows("netstat -ano");
                for (String string : strings) {
                    final String[] split = string.split("\n");
                    for (String s : split) {
                        final Matcher matcher = compile.matcher(s);
                        if (matcher.find()) {
                            final String group = matcher.group(0);
                            final String[] split1 = group.split(":");
                            integers.add(Integer.valueOf(split1[split1.length - 1]));
                        }
                    }
                }
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        } else {
            try {
                final String[] strings = ExecUtil.execCmdOnLinux("netstat -tunpl");
                for (String string : strings) {
                    final String[] split = string.split("\n");
                    for (String s : split) {
                        final Matcher matcher = compile.matcher(s);
                        if (matcher.find()) {
                            final String group = matcher.group(0);
                            final String[] split1 = group.split(":");
                            integers.add(Integer.valueOf(split1[split1.length - 1]));
                        }
                    }
                }
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        }

        return integers;
    }

    public static String execute(String command, boolean autoLog) {
        if (command == null) {
            return null;
        }
        String[] commands = {"/bin/bash", "-c", command};
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(commands);
            String code = "UTF-8";
            String successResult = readInputStream(process.getInputStream(), code);
            String errorResult = readInputStream(process.getErrorStream(), code);
            if (StringUtils.isNotBlank(successResult) && !"null".equals(successResult) && autoLog) {
                Log.low.info("read process inputStream : " + successResult);
            }
            if (StringUtils.isNotBlank(errorResult) && !"null".equals(errorResult) && autoLog) {
                Log.low.info("read process errorStream : " + successResult);
            }
            return successResult;
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
        return null;
    }

    public static String executeWindows(String command) {
        if (command == null) {
            return null;
        }
        String[] commands = {"cmd", "/c", command};
        Process process = null;
        try {
            process = Runtime.getRuntime().exec(commands);
            String code = "GBK";

            String successResult = readInputStream(process.getInputStream(), code);
            String errorResult = null;
            if (StringUtils.isNotBlank(successResult)) {
                Log.low.info("read process inputStream : " + successResult);
            } else {
                errorResult = readInputStream(process.getErrorStream(), code);
            }
            if (StringUtils.isNotBlank(errorResult)) {
                Log.low.info("read process errorStream : " + successResult);
            }
            if (StringUtils.isNotEmpty(successResult)) {
                return successResult;
            } else {
                return errorResult;
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        } finally {
            if (process != null) {
                process.destroy();
            }
        }
        return null;
    }

    /**
     * 只读一行!
     */
    public static String readInputStream(InputStream is, String code) throws IOException {
        return FileUtil.readInputStream(is, code);
    }


    public static String getProcessBaseDir() {
        if (osName.contains("windows")) {
            return "D:\\dist\\";
        } else if (osName.contains("linux")) {
            return "/dist/";
        }

        return null;
    }

    /**
     * 根据操作环境来获取JVM的PID
     *
     * @return
     */
    public static String getPidByOs() {
        if (osName.toLowerCase().startsWith("windows")) {
            return getJvmPIDOnWindows();
        } else if (osName.toLowerCase().startsWith("linux")) {
            return getJvmPIDOnLinux();
        }
        return null;
    }


    /**
     * windows环境下获取JVM的PID
     *
     * @return
     */
    private static String getJvmPIDOnWindows() {
        RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
        return runtime.getName().split("@")[0];
    }

    /**
     * linux环境下获取JVM的PID
     *
     * @return
     */
    private static String getJvmPIDOnLinux() {
      /*  String fi = System.getProperty("service-jar");
        String command = "ps x |grep " + fi + " |awk '{print $1}'";*/
        String command = "pidof java";
        BufferedReader in = null;
        Process pro = null;
        try {
            pro = Runtime.getRuntime().exec(new String[]{"sh", "-c", command});
            in = new BufferedReader(new InputStreamReader(pro.getInputStream()));
            StringTokenizer ts = new StringTokenizer(in.readLine());
            String pid = ts.nextToken();
            return pid;
        } catch (IOException e) {
            Log.high.error("getJvmPIDOnLinux error", e);
        }
        return null;
    }

}
