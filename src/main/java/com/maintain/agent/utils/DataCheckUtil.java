package com.maintain.agent.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;

/**
 * 数据检查工具类
 * 提供字符串和IP地址的验证功能，包括：
 * 1. 字符串空值检查
 * 2. IP地址格式验证
 * 3. IP地址提取
 * 4. 正则表达式匹配
 * 
 * 主要功能：
 * - 提供字符串空值检查方法
 * - 支持IPv4地址格式验证
 * - 支持从文本中提取IP地址
 * - 提供通用的正则表达式匹配功能
 * - 使用预编译的正则表达式提高性能
 * 
 * <AUTHOR>
 * @date 2016/10/28
 */
public class DataCheckUtil {
    /**
     * IP地址正则表达式模式
     * 匹配标准IPv4地址格式
     */
    private static Pattern ipPattern;

    static {
        String ipRegExp = "((?:(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d)))\\.){3}(?:25[0-5]|2[0-4]\\d|((1\\d{2})|([1-9]?\\d))))";
        ipPattern = Pattern.compile(ipRegExp);
    }

    /**
     * 检查字符串是否为空
     * 当字符串为null或空字符串时返回true
     *
     * @param value 要检查的字符串
     * @return 如果字符串为空返回true，否则返回false
     */
    public static boolean isBlank(String value) {
        return (value == null || value.isEmpty());
    }

    /**
     * 检查字符串是否为有效的IPv4地址
     * 使用正则表达式验证IP地址格式
     *
     * @param value 要检查的字符串
     * @return 如果是有效的IPv4地址返回true，否则返回false
     */
    public static boolean isIP(String value) {
        if (value.isEmpty() || value.length() < 7 || value.length() > 15) {
            return false;
        }
        Matcher matcher = ipPattern.matcher(value);
        return matcher.matches();
    }

    /**
     * 检查字符串中是否包含IPv4地址
     * 使用正则表达式查找IP地址
     *
     * @param value 要检查的字符串
     * @return 如果包含IPv4地址返回true，否则返回false
     */
    public static boolean hasIP(String value) {
        if (value.isEmpty() || value.length() < 7) {
            return false;
        }
        Matcher matcher = ipPattern.matcher(value);
        return matcher.find();
    }

    /**
     * 从字符串中提取IPv4地址
     * 返回找到的第一个有效IP地址
     *
     * @param value 要检查的字符串
     * @return 找到的IPv4地址，如果没有找到则返回null
     */
    public static String getIP(String value) {
        if (value.isEmpty() || value.length() < 7) {
            return null;
        }
        Matcher matcher = ipPattern.matcher(value);
        if (matcher.find()) {
            return matcher.group();
        }
        return null;
    }

    /**
     * 检查字符串是否匹配指定的正则表达式模式
     * 返回第一个匹配的子串
     *
     * @param value 要检查的字符串
     * @param patternStr 正则表达式模式
     * @return 匹配的子串，如果没有匹配则返回null
     */
    public static String hasPattern(String value, String patternStr) {
        if (value == null || value.isEmpty()) {
            return null;
        }
        if (patternStr == null || patternStr.isEmpty()) {
            return null;
        }

        Pattern pattern;
        try {
            pattern = Pattern.compile(patternStr);
        } catch (PatternSyntaxException e) {
            return null;
        }
        Matcher matcher = pattern.matcher(value);
        if (matcher.find()) {
            return matcher.group();
        } else {
            return null;
        }
    }
}
