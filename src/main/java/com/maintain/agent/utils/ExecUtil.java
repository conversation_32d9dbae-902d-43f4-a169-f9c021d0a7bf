package com.maintain.agent.utils;

import com.common.log.Log;
import com.maintain.agent.business.thread.ReadStreamThread;
import com.maintain.agent.conf.Constants;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 命令执行工具类
 * 提供在Windows和Linux系统上执行命令的功能
 * 支持同步执行命令并获取执行结果
 * 处理命令的标准输出和错误输出
 * 
 * 主要功能：
 * - 支持Windows和Linux系统命令执行
 * - 提供Shell和Python命令执行
 * - 支持命令执行结果的获取
 * - 处理标准输出和错误输出
 * - 使用多线程读取命令输出
 * - 自动选择正确的字符编码
 * - 提供命令执行超时控制
 * 
 * <AUTHOR>
 * @date 2016/12/13
 */
public class ExecUtil {

    /**
     * 在Linux系统上执行Shell命令
     * 使用/bin/bash作为命令解释器
     *
     * @param command 要执行的Shell命令
     * @return 命令执行结果数组，[0]为标准输出，[1]为错误输出
     * @throws IOException 当命令执行失败时抛出
     */
    public static String[] execCmdOnLinux(String command) throws IOException {
        //Log.low.debug("执行命令： " + command);
        String[] commands = new String[]{"/bin/bash", "-c", command};
        return exec(commands);
    }

    /**
     * 在Linux系统上执行Python命令
     * 使用系统默认的Python解释器
     *
     * @param command 要执行的Python命令
     * @return 命令执行结果数组，[0]为标准输出，[1]为错误输出
     * @throws IOException 当命令执行失败时抛出
     */
    public static String[] execCmdOnLinuxForPython(String command) throws IOException {
        String[] commands = new String[]{"", "", command};
        return exec(commands);
    }

    public static void main(String[] args) throws Exception {
        System.out.println(Charset.defaultCharset());
    }

    /**
     * 在Windows系统上执行CMD命令
     * 使用cmd.exe作为命令解释器
     *
     * @param command 要执行的CMD命令
     * @return 命令执行结果数组，[0]为标准输出，[1]为错误输出
     * @throws IOException 当命令执行失败时抛出
     */
    public static String[] execCmdOnWindows(String command) throws IOException {
        String[] commands = new String[]{"cmd", "/c", command};
        return exec(commands);
    }

    /**
     * 执行系统命令的通用方法
     * 支持执行任意命令数组，并返回执行结果
     *
     * @param commands 要执行的命令数组，通常包含命令解释器和具体命令
     * @return 命令执行结果数组，[0]为标准输出，[1]为错误输出
     * @throws IOException 当命令执行失败时抛出
     */
    public static String[] exec(String[] commands) throws IOException {
        Runtime runtime = Runtime.getRuntime();
        Process process = runtime.exec(commands);

        Map<String, String> resultMap = readStream(process);

        String error = resultMap.get(Constants.PROC_RESULT_OUT_TYPE_ERROR);
        String out = resultMap.get(Constants.PROC_RESULT_OUT_TYPE_OUT);

        process.destroy();

        String[] res = {out, error};
        return res;
    }

    /**
     * 读取命令执行进程的输出流
     * 使用多线程同时读取标准输出和错误输出
     * 根据操作系统自动选择正确的字符编码
     *
     * @param process 要读取输出的进程
     * @return 包含标准输出和错误输出的Map，key为输出类型，value为输出内容
     */
    public static Map<String, String> readStream(Process process) {
        InputStream errStream = process.getErrorStream();
        InputStream outStream = process.getInputStream();
        StringBuilder errBuilder = new StringBuilder();
        StringBuilder outBuilder = new StringBuilder();

        List<Runnable> threadList = new ArrayList<>();
        String code = Constants.ENCODE_GBK;
        if (!ProcessUtil.isWindows()) {
            code = Constants.ENCODE_UTF8;
        }
        threadList.add(new ReadStreamThread(errStream, errBuilder, code));
        threadList.add(new ReadStreamThread(outStream, outBuilder, code));

        ThreadUtil.executeMultiThread(threadList, 2);

        Map<String, String> map = new HashMap<>();
        map.put(Constants.PROC_RESULT_OUT_TYPE_ERROR, errBuilder.toString());
        map.put(Constants.PROC_RESULT_OUT_TYPE_OUT, outBuilder.toString());
        return map;
    }

    /**
     * 执行CMD命令的简化方法
     * 直接使用Runtime.exec执行命令字符串
     *
     * @param commend 要执行的CMD命令字符串
     * @return 命令执行结果数组，[0]为标准输出，[1]为错误输出
     * @throws IOException 当命令执行失败时抛出
     */
    public static String[] runCmdCommend(String commend) throws IOException {
        Process process = Runtime.getRuntime().exec(commend);
        Map<String, String> resultMap = readStream(process);
        String error = resultMap.get(Constants.PROC_RESULT_OUT_TYPE_ERROR);
        String out = resultMap.get(Constants.PROC_RESULT_OUT_TYPE_OUT);
        process.destroy();
        String[] res = {out, error};
        return res;
    }

}
