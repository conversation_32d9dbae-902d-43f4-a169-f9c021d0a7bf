package com.maintain.agent.utils;

import java.util.List;

/**
 * 列表操作工具类
 * 提供List集合的常用判断方法，包括：
 * 1. 判断列表是否为空
 * 2. 判断列表是否非空
 * 
 * 主要功能：
 * - 提供通用的列表空值检查方法
 * - 支持泛型类型的列表判断
 * - 统一的空值判断逻辑（null和size=0）
 * - 线程安全的工具方法
 * 
 * <AUTHOR>
 */
public final class ListUtil {

	private ListUtil() {
	}

	/**
	 * 判断列表是否为空
	 * 当列表为null或size为0时返回true
	 *
	 * @param <T> 列表元素类型
	 * @param list 要判断的列表
	 * @return 如果列表为空返回true，否则返回false
	 */
	public static <T> Boolean isEmpty(List<T> list) {
		return list == null || list.size() <= 0;
	}

	/**
	 * 判断列表是否非空
	 * 当列表不为null且size大于0时返回true
	 *
	 * @param <T> 列表元素类型
	 * @param list 要判断的列表
	 * @return 如果列表非空返回true，否则返回false
	 */
	public static <T> Boolean isNotEmpty(List<T> list) {
		return !isEmpty(list);
	}

}
