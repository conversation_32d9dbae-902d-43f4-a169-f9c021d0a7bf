package com.maintain.agent.business.handler;

/**
 * <AUTHOR>
 * @date 2016/10/31
 * 服务的状态操作类
 */
public class ServiceStatusHandler {
    /**
     * 状态
     */
    private volatile boolean status = false;

    /**
     * 此类的实例
     */
    private static ServiceStatusHandler instance = new ServiceStatusHandler();

    /**
     * 获得类的实例
     *
     * @return 返回类的实例
     */
    public static ServiceStatusHandler getInstance() {
        return instance;
    }

    private ServiceStatusHandler() {
    }

    /**
     * 获得状态
     *
     * @return 返回状态
     */
    public boolean isAlive() {
        return status;
    }

    /**
     * 状态赋值
     *
     * @param status 状态
     */
    public void setStatus(boolean status) {
        this.status = status;
    }
}
