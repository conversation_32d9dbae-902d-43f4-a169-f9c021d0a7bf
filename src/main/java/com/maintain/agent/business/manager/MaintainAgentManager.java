package com.maintain.agent.business.manager;

import com.common.log.Log;
import com.common.util.Utils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.maintain.agent.business.handler.ServiceStatusHandler;
import com.maintain.agent.business.helper.BusinessHelper;
import com.maintain.agent.business.helper.OperationHelper;
import com.maintain.agent.business.helper.ReqRespHelper;
import com.maintain.agent.business.helper.RestartHelper;
import com.maintain.agent.business.service.HardInfo;
import com.maintain.agent.business.service.MonitoringServiceImpl;
import com.maintain.agent.business.thread.StopAgentThread;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.request.*;
import com.maintain.agent.entity.response.BaseResponse;
import com.maintain.agent.entity.struct.*;
import com.maintain.agent.server.SoftwareMonitor;
import com.maintain.agent.utils.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;


/**
 * 运维代理管理器
 * 负责管理和协调运维Agent的所有核心功能，包括：
 * 1. 系统监控（硬件、软件、进程等）
 * 2. 日志管理
 * 3. 文件操作
 * 4. 进程管理
 * 5. 时间同步
 * 6. 与Server端的通信
 * 
 * <AUTHOR>
 * @date 2016/10/31
 */
public class MaintainAgentManager {
    /**
     * 单例实例
     */
    private static MaintainAgentManager instance = new MaintainAgentManager();

    /**
     * 主监控线程休眠时间（3分钟）
     */
    private static final Long SLEEP_TIME = 180 * 1000L;

    /**
     * 日志监控线程休眠时间（10分钟）
     */
    private static final Long LOG_SLEEP_TIME = 600 * 1000L;

    /**
     * 脚本执行线程休眠时间（1分钟）
     */
    private static final Long SCRIPT_SLEEP_TIME = 60 * 1000L;

    /**
     * 系统操作辅助类，用于执行系统级操作
     */
    private OperationHelper operationHelper;

    /**
     * 线程池执行器
     * 核心线程数：6
     * 最大线程数：10
     * 线程存活时间：5分钟
     * 使用同步队列
     */
    private ThreadPoolExecutor executorService = new ThreadPoolExecutor(6, 10, 5, TimeUnit.MINUTES, new SynchronousQueue<>(false), new ThreadFactory() {
        private AtomicInteger count = new AtomicInteger(0);

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            t.setName("softwareMonitorThread-" + count.incrementAndGet());
            if (!t.isDaemon()) {
                t.setDaemon(true);
            }
            return t;
        }
    });

    /**
     * 获取MaintainAgentManager的单例实例
     * 
     * @return MaintainAgentManager实例
     */
    public static MaintainAgentManager getInstance() {
        return instance;
    }

    /**
     * 初始化运维代理
     * 执行以下初始化步骤：
     * 1. 初始化操作辅助类
     * 2. 清理旧的监控数据
     * 3. 启动FTP日志清理定时任务
     * 4. 启动软件监控线程
     * 5. 启动软件信息发送线程
     * 
     * @return 初始化是否成功
     */
    public boolean init() {


        //初始化操作辅助类，每分钟刷新一次进程信息和ice进程信息
        if (!initOperationHelper()) {
            Log.high.error("InitService initOperationHelper fail");
            return false;
        }

        try {
            String hardwareMonitoringDir;
            String softwareMonitoringDir;
            if (HardInfo.isWindows()) {
                hardwareMonitoringDir = Config.getSystemConfig().getHardwareMonitoringDirWindow();
                softwareMonitoringDir = Config.getSystemConfig().getSoftwareMonitoringDirWindow();
            } else if (HardInfo.isLinux()) {
                hardwareMonitoringDir = Config.getSystemConfig().getHardwareMonitoringDirLinux();
                softwareMonitoringDir = Config.getSystemConfig().getSoftwareMonitoringDirLinux();
            } else {
                throw new Exception("未知操作系统");
            }

            //OOM问题定位
           /* String hardwareMonitoringDir;
            String softwareMonitoringDir;
            String[] hardwareResult;
            String[] softwareResult;
            if (HardInfo.isWindows()) {
                hardwareMonitoringDir = Config.getSystemConfig().getHardwareMonitoringDirWindow();
                softwareMonitoringDir = Config.getSystemConfig().getSoftwareMonitoringDirWindow();
                String hardwareCommand = "rd /s /q " + hardwareMonitoringDir;
                hardwareResult = ExecUtil.execCmdOnWindows(hardwareCommand);
                Log.low.info("清除monitoring下hardware文件夹旧监控数据结果: " + hardwareResult);
                String softwareCommand = "rd /s /q " + softwareMonitoringDir;
                softwareResult = ExecUtil.execCmdOnWindows(softwareCommand);
                Log.low.info("清除monitoring下software文件夹旧监控数据结果: " + softwareResult);
            } else if (HardInfo.isLinux()) {
                hardwareMonitoringDir = Config.getSystemConfig().getHardwareMonitoringDirLinux();
                softwareMonitoringDir = Config.getSystemConfig().getSoftwareMonitoringDirLinux();
                //1.mkdir /dist/monitoring/blank
                //2./bin/bash -c rsync --delete-before -d /dist/monitoring/blank /dist/monitoring/software

                ///bin/bash -c find /home/<USER>/hardware/ -type f|xargs rm -rf
                String hardwareCommand = "find " + hardwareMonitoringDir + " -type f|xargs rm -rf";
                hardwareResult = ExecUtil.execCmdOnLinux(hardwareCommand);
                Log.low.info("清除monitoring下hardware文件夹旧监控数据结果: " + hardwareResult);
                String softwareCommand = "find " + softwareMonitoringDir + " -type f|xargs rm -rf";
                softwareResult = ExecUtil.execCmdOnLinux(softwareCommand);
                Log.low.info("清除monitoring下software文件夹旧监控数据结果: " + softwareResult);
            } else {
                throw new Exception("未知操作系统");
            }*/


            //删除旧的监控数据，如果监控数据特别特别多，这个初始化方法就会阻塞，除非改用异步删
           /* new Thread(()->{

            }).start();*/
            FileUtil.deleteDir(new File(hardwareMonitoringDir));
            FileUtil.deleteDir(new File(softwareMonitoringDir));
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        } finally {
            Log.low.info("清除monitor文件完毕");
        }

        //启动FTP服务器
        /*if (!startFtpServer()) {
            Log.high.error("InitService startFtpServer fail");
            return false;
        }*/

        //启动过期FTP日志文件清除定时器
        if (!clearFtpLogScheduled()) {
            Log.high.error("InitService clearFtpLogScheduled fail");
            return false;
        }

        //建立与Server端的连接
        /*if (!MaintainAgentManager.getInstance().connectToServerRetry()) {
            Log.high.error("InitService connectToServerRetry fail");
            return false;
        }*/
//==========================v1.0版本增加功能============================================

        executorService.submit(() -> {
            while (true) {
                try {
                    SoftwareMonitor.INSTANCE.monitorSoftware();
                } catch (Exception e) {
                    Log.high.error("程序监控出现异常", e);
                }
                try {
                    Thread.sleep(SLEEP_TIME);
                } catch (Exception e) {
                }
            }
        });
        executorService.submit(() -> {
            while (true) {
                try {
                    SoftwareMonitor.INSTANCE.sendSoftwareInfo();
                } catch (JsonProcessingException e) {
                    Log.high.error("发送程序信息给serverjson转换异常", e);
                } catch (Exception e) {
                    Log.high.error("发送程序信息给server发生异常", e);
                }
                try {
                    Thread.sleep(SLEEP_TIME);
                } catch (Exception e) {
                }
            }
        });

        executorService.submit(() -> {
            while (true) {
                try {
                    SoftwareMonitor.INSTANCE.monitorLog();
                } catch (Exception e) {
                    Log.high.error("监控日志异常", e);
                }
                try {
                    Thread.sleep(LOG_SLEEP_TIME);
                } catch (Exception e) {
                }
            }
        });

        executorService.submit(() -> {
            while (true) {
                try {
                    SoftwareMonitor.INSTANCE.monitorStartScript();
                } catch (Exception e) {
                    Log.high.error("监控启动脚本异常", e);
                }
                try {
                    Thread.sleep(SCRIPT_SLEEP_TIME);
                } catch (Exception e) {
                }
            }
        });

        executorService.submit(() -> {
            for (; ; ) {
                checkNtp();
                try {
                    TimeUnit.HOURS.sleep(1);
                } catch (Exception e) {
                }
            }
        });

        //主机硬件监控数据生成定时器,每分钟发送一次硬件信息
        hostMonitoringScheduled();

        //系统软件监控数据抓取定时器,每分钟发送一次软件信息
        softwareMonitoringScheduled();

        //定时删除异常文件，每天删除一次
        deleteErrorFileScheduled();

        //设置服务状态为开启
        openService();

//==========================v1.4版本增加功能============================================
        //假死干预机制调度
        restartScheduled();

        //所有初始化工作完成，返回成功
        Log.low.info("InitService succeed");
//==========================v1.6版本增加功能============================================
        //开启NTP服务端服务
        openNtpService();

        //业务监控
        businessMonitor();
        return true;
    }

    /**
     * 检查NTP服务状态
     * 验证系统时间同步服务是否正常运行
     */
    private void checkNtp() {
        String ntpIp = Config.getSystemConfig().getNtpServer();
        final String hostIP = Utils.getHostIP();
        Integer status;
        if (ProcessUtil.isWindows()) {
            Runtime runtime = Runtime.getRuntime();
            try {
                String baseKeyName = "\"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\W32Time\\Parameters\" /v Type";
                String queryCommand = "REG QUERY " + baseKeyName;
                Process process = runtime.exec(queryCommand);
                Map<String, String> map = ExecUtil.readStream(process);
                String result = map.get(Constants.PROC_RESULT_OUT_TYPE_OUT);
                // 先看W32Time是不是NTP服务(默认就是NTP)
                if (StringUtils.isEmpty(result) || !result.contains(Constants.NTP)) {
                    status = 0;
                } else {
                    result = ProcessUtil.executeWindows("net start").toLowerCase();
                    if (result.contains("w32time") || result.contains("windows time")) {
                        status = 1;
                    } else {
                        status = 0;
                    }
                }
            } catch (Exception e) {
                Log.high.error(e);
                return;
            }
        } else {
            String baseNtp = "service ntpd ";
            String ntpStatus = ProcessUtil.execute(baseNtp + "status", true);
            boolean running = ntpStatus != null && (ntpStatus.contains("running") || ntpStatus.contains("运行"));
            if (checkNtp(ntpIp)) {
                if (!running) {
                    //没启动，
                    status = 0;
                } else {
                    status = 1;
                }
            } else {
                status = 0;
            }
        }
        ServerRequestUtil.request(20057, hostIP + "," + status);
    }

    /**
     * 开启NTP服务
     * 根据操作系统类型选择相应的NTP服务启动方式
     */
    public void openNtpService() {
        Integer autoNtp = Config.getSystemConfig().getAutoNtp();
        if (autoNtp != null && 0 == autoNtp) {
            return;
        }
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains(Constants.OS_WINDOWS)) {
            windowsNtp();
        } else if (osName.contains(Constants.OS_LINUX)) {
            linuxNtp();
        }
    }

    /**
     * 在Linux系统上配置NTP服务
     * 包括安装、配置和启动NTP服务
     */
    public synchronized void linuxNtp() {
        String ip = Config.getSystemConfig().getNtpServer();
        String baseNtp = "service ntpd ";
        String ntpStatus = ProcessUtil.execute(baseNtp + "status", true);
        boolean running = ntpStatus != null && (ntpStatus.contains("running") || ntpStatus.contains("运行"));
        if (checkNtp(ip)) {
            if (!running) {
                //没启动，重启就算了
                ProcessUtil.execute(baseNtp + "start", true);
            }
            return;
        }
        if (running) {
            Log.low.info("Ntp service is running....");
            ProcessUtil.execute(baseNtp + "stop", true);
        }
        String command1 = "echo -e \"SELINUX=disabled\\nSELINUXTYPE=targeted\" > /etc/selinux/config";

        String comm = "echo -e  \"ftfile /var/lib/ntp/drift\n" +
                "driftfile /var/lib/ntp/drift\n" +
                "server %s\n" +
                "fudge *********** stratum 10\n" +
                "restrict 0.0.0.0  mask 0.0.0.0 nomodify notrap\n" +
                "includefile /etc/ntp/crypto/pw\n" +
                "keys /etc/ntp/keys\ntos maxdis 20 \" > /etc/ntp.conf";
        String command2 = String.format(comm, ip);
//        String command3 = String.format("ntpdate -u %s", ip);
        String command4 = "chkconfig ntpd on";
        String command5 = baseNtp + "start";
        ProcessUtil.executeLinux(true, command1, command2, command4, command5);
    }

    /**
     * 检查指定IP的NTP服务是否可用
     * 
     * @param ip NTP服务器IP地址
     * @return NTP服务是否可用
     */
    private boolean checkNtp(String ip) {
        String config = ProcessUtil.execute("cat /etc/ntp.conf", true);
        if (config != null && (config.contains(ip) || config.contains("NN"))) {
            //配置文件里面已经有server的NTP配置信息
            return true;
        }
        return false;
    }

    /**
     * 在Windows系统上配置NTP服务
     * 包括修改注册表配置和启动Windows时间服务
     */
    public synchronized void windowsNtp() {
        Runtime runtime = Runtime.getRuntime();
        try {
            String baseKeyName = "\"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\W32Time\\Parameters\" /v Type";
            String queryCommand = "REG QUERY " + baseKeyName;
            Process process = runtime.exec(queryCommand);
            Map<String, String> map = ExecUtil.readStream(process);
            String result = map.get(Constants.PROC_RESULT_OUT_TYPE_OUT);
            // 先看W32Time是不是NTP服务(默认就是NTP)
            if (StringUtils.isEmpty(result) || !result.contains(Constants.NTP)) {
                String type = "REG_SZ";
                modifyReg(baseKeyName, type, Constants.NTP, runtime);
                Log.low.info("set W32Time to NTP service finish");
            }
            String serverIp = Config.getSystemConfig().getNtpServer();
            if (StringUtils.isEmpty(serverIp)) {
                Log.high.error("MainTainServerIp is null");
                return;
            }
            // 修改NtpServer的Ip
            baseKeyName = "\"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\W32Time\\Parameters\" /v NtpServer";
            queryCommand = "REG QUERY " + baseKeyName;
            process = runtime.exec(queryCommand);
            map = ExecUtil.readStream(process);
            result = map.get(Constants.PROC_RESULT_OUT_TYPE_OUT);
            if (StringUtils.isEmpty(result) || !result.contains(serverIp)) {
                String type = "REG_SZ";
                modifyReg(baseKeyName, type, serverIp + ",0x9", runtime);
            }
            // 修改NTP同步时间周期，单位：秒 30
            String type = "REG_DWORD";
            String second = "300";
            baseKeyName = "\"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\W32Time\\TimeProviders\\NtpClient\" /v SpecialPollInterval";
            modifyReg(baseKeyName, type, second, runtime);
//            // 修改更新间隔时间
//            baseKeyName = "\"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\W32Time\\TimeProviders\\Config\" /v UpdateInterval";
//            modifyReg(baseKeyName, type, second, runtime);
            // 修改时间同步服务器
            baseKeyName = "\"HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\W32Time\\TimeProviders\\NtpClient\" /v SpecialPollTimeRemaining";
            type = "REG_MULTI_SZ";
            modifyReg(baseKeyName, type, serverIp + ",0", runtime);
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException e) {
                Log.high.error(e);
            }
            // 重启服务
            runtime.exec("NET STOP W32TIME");
            try {
                TimeUnit.SECONDS.sleep(5);
            } catch (InterruptedException e) {
                Log.high.error(e);
            }
            runtime.exec("NET START W32TIME");
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    /**
     * 修改Windows注册表
     * 
     * @param keyName 注册表键名
     * @param type 值类型
     * @param value 值
     * @param runtime 运行时实例
     * @throws IOException 当修改注册表失败时抛出
     */
    private void modifyReg(String keyName, String type, String value, Runtime runtime) throws IOException {
        //删除旧的注册表
        String delCommand = "REG DELETE " + keyName + " /f";
        runtime.exec(delCommand);
        try {
            //延迟执行
            Thread.sleep(500L);
        } catch (InterruptedException e) {
            Log.high.error(e.getMessage(), e);
        }
        //然后重新加上去
        String addCommand = "REG ADD " + keyName + " /t " + type + " /d " + value + " /f";
        runtime.exec(addCommand);
    }

    /**
     * 启动定时重启任务
     * 定期检查系统状态并在必要时执行重启
     */
    private void restartScheduled() {
        if (Config.getRestartConfig().isUse()) {
            Date date = new Date();
            new Timer().schedule(new TimerTask() {
                @Override
                public void run() {
                    List<RestartStruct> list = RestartHelper.getNeedRestartProcess();
                    RestartHelper.restartProcess(list);
                }
            }, date, Config.getRestartConfig().getRestartTimeMin() * Constants.ONE_MINUTE_MILLISECONDS);
        }
    }

    /**
     * 启动软件监控定时任务
     * 定期监控已安装软件的状态
     */
    private void softwareMonitoringScheduled() {
        Date date = new Date();
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    Thread.currentThread().setName("Software-Thread");
                    MonitoringServiceImpl.readFileAndSendToServer(MonitoringServiceImpl.getSoftwareMonitoringDir(), Constants.AGENT_SEND_SOFTWARE_MONITORING_DATA);
                } catch (Exception e) {
                    Log.high.error("readFileAndSendToServer software error," + e);
                }
            }
        }, date, Config.getSystemConfig().getSendMonitorTime() * Constants.ONE_SECOND_MILLISECONDS);
    }

    /**
     * 启动错误文件清理定时任务
     * 定期清理过期的错误日志文件
     */
    private void deleteErrorFileScheduled() {
        Date date = new Date();
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                File[] files = new File(".").listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.isFile() && file.getName().startsWith("hs_err_pid")) {
                            file.delete();
                        }
                    }
                }
            }
        }, date, Constants.ONE_DAY_MILLISECONDS);
    }

    /**
     * 启动主机监控定时任务
     * 定期收集和上报主机状态信息
     */
    private void hostMonitoringScheduled() {
        File file = new File(MonitoringServiceImpl.getHardwareMonitoringDir());
        if (!file.exists()) {
            try {
                FileUtils.forceMkdir(file);
            } catch (IOException e) {
                Log.high.error("make dir error,path=" + MonitoringServiceImpl.getHardwareMonitoringDir() + ",msg:" + e);
            }
        }
        //启动定时器
        long period = Config.getSystemConfig().getSendMonitorTime() * Constants.ONE_SECOND_MILLISECONDS;
        executorService.submit(() -> {
            Thread.currentThread().setName("Hardware-Thread");
            while (true) {
                try {
                    MonitoringServiceImpl.mainOfHardwareMonitoring();
                } catch (Exception e) {
                    Log.high.error("host monitoring error," + e.getMessage(), e);
                }
                try {
                    Thread.sleep(period);
                } catch (Exception e) {
                }
            }
        });
    }


    /**
     * 启动业务监控
     * 监控关键业务进程和服务的状态
     */
    private void businessMonitor() {
        Date date = new Date();
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    scanDir();
                } catch (Exception e) {
                    Log.high.error(e.getMessage());
                }
            }
        }, date, Constants.ONE_DAY_MILLISECONDS);
    }


    /**
     * 扫描目录
     * 递归扫描指定目录下的所有文件和子目录
     */
    public static void scanDir() {
        String osName = System.getProperty("os.name").toLowerCase();
        String baseDir = "";
        if (osName.contains(Constants.OS_WINDOWS)) {
            baseDir = "D:\\dist";
        } else if (osName.contains(Constants.OS_LINUX)) {
            baseDir = "/dist";
        }
        File file = new File(baseDir);
        if (!file.exists()) {
            return;
        }
        File[] firstFloorDir = file.listFiles();
        if (firstFloorDir == null || firstFloorDir.length == 0) {
            return;
        }
        for (File programFile : firstFloorDir) {
            File[] secondFloorDir = programFile.listFiles();
            if (secondFloorDir == null || secondFloorDir.length == 0) {
                continue;
            }
            for (File monitorFile : secondFloorDir) {
                String fileName = monitorFile.getName();
                if ("monitor".equalsIgnoreCase(fileName)) {
                    ExecutorService threadPool = Executors.newCachedThreadPool();
                    try {
                        File[] monitorFiles = monitorFile.listFiles();
                        if (monitorFiles == null || monitorFiles.length == 0) {
                            continue;
                        }
                        for (File monitorDataFile : monitorFiles) {
                            if (monitorDataFile != null && monitorDataFile.exists()) {
                                String param = FileUtils.readFileToString(monitorDataFile, Constants.ENCODE_UTF8);
                                if (StringUtils.isNotEmpty(param)) {
                                    Map map = Config.getObjectMapper().readValue(param, Map.class);
                                    String name = monitorDataFile.getName();
                                    if (name.contains("-")) {
                                        String[] params = name.split("-");
                                        map.put("name", params[0]);
                                        String createTime = params[1];
                                        if (createTime != null) {
                                            createTime = createTime.replace(".json", "");
                                            if (createTime.length() > 14) {
                                                createTime = createTime.substring(0, 14);
                                            }
                                        }
                                        map.put("createTime", createTime);
                                    }
                                    map.put("ip", DataCheckUtil.getIP(Config.getSystemConfig().getMaintainAgent()));
                                    String str = Config.getObjectMapper().writeValueAsString(map);
                                    String result = ServerRequestUtil.request(Constants.BUSINESS_MONITOR, str);
                                    if (StringUtils.isNotEmpty(result)) {
                                        threadPool.execute(() -> {
                                            try {
                                                FileUtils.forceDelete(monitorDataFile);
                                            } catch (IOException e) {
                                                Log.high.error(e.getMessage(), e);
                                            }
                                        });
                                    }
                                }
                            }
                        }
                        // File monitorDataFile = getLastestFile(monitorFiles);
                    } catch (IOException e) {
                        Log.high.error(e.getMessage(), e);
                    } finally {
                        threadPool.shutdown();
                    }
                }
            }
        }
    }


    /**
     * 初始化操作辅助类
     * 创建并启动操作辅助类的定时任务
     * 
     * @return 初始化是否成功
     */
    private boolean initOperationHelper() {
        operationHelper = new OperationHelper();
        if (!operationHelper.updateCacheAndMap()) {
            Log.high.error("initOperationHelper updateCacheAndMap fail");
            return false;
        }
        //启动定时器
        long period = Config.getSystemConfig().getUpdateCacheInterval();
        if (period <= 0) {
            Log.high.error("initOperationHelper [period:" + period + "] error");
            return false;
        }
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                operationHelper.updateCacheAndMap();
            }
        }, period, period);
        Log.low.info("initOperationHelper succeed");
        return true;
    }

    /**
     * 启动FTP日志清理定时任务
     * 定期清理过期的FTP日志文件
     * 
     * @return 启动是否成功
     */
    private boolean clearFtpLogScheduled() {
        String ftpDir = Config.getSystemConfig().getFtpDir();
        final File dir = new File(ftpDir);
        if (!dir.exists()) {
            dir.mkdirs();
        }

        //清理过期的FTP日志文件
        if (!clearFtpLog(dir)) {
            Log.high.error("clearFtpLogScheduled clearErrFile fail");
            return false;
        }

        //启动定时器
        long period = 12 * Constants.ONE_HOUR_MILLISECONDS;
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                clearFtpLog(dir);
            }
        }, period, period);
        Log.low.info("clearFtpLogScheduled succeed");
        return true;
    }

    /**
     * 清理FTP日志文件
     * 删除指定目录下的过期日志文件
     * 
     * @param dir 日志目录
     * @return 清理是否成功
     */
    private boolean clearFtpLog(File dir) {
        if (!dir.exists() || !dir.isDirectory()) {
            return false;
        }
        //获取该目录下的子目录（子目录日期作为目录名）
        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            return true;
        }
        //删除一天前的子目录
        for (File file : files) {
            if (!file.exists() || !file.isDirectory()) {
                continue;
            }
            long createTime = BusinessHelper.getFileCreateTime(file.getPath());
            if (createTime <= 0) {
                continue;
            }
            if (System.currentTimeMillis() - createTime >= Constants.ONE_DAY_MILLISECONDS) {
                FileUtils.deleteQuietly(file);
            }
        }
        return true;
    }


    /**
     * 连接到服务器
     * 建立与运维服务器的连接
     * 
     * @return 连接是否成功
     */
    private synchronized boolean connectToServer() {

        try {
            String result = ServerRequestUtil.request(Constants.PING, "ping");
            if (StringUtils.isNotEmpty(result)) {
                BaseResponse response = Config.getObjectMapper().readValue(result, BaseResponse.class);
                if (response != null && response.getCode() == Constants.SUCCESS_RESPONSE_CODE) {
                    Log.low.info("connectToServer succeed");
                    return true;
                }
            }
        } catch (Exception e) {
            Log.high.error("connectToServer failed.", e);
            return false;
        }
        return false;
    }

    /**
     * 关闭服务代理
     * 停止所有监控任务并清理资源
     * 
     * @return 关闭是否成功
     */
    public boolean closeServiceAgent() {
        //服务开启后才能提供关闭功能
        /*if (!isServiceAlive()) {
            Log.high.error("closeServiceAgent [serviceIsAlive:false] error");
            return false;
        }

        //设置服务状态为关闭
        shutService();*/

        new Thread(new StopAgentThread()).start();

        //正常关闭服务
        Log.low.info("closeServiceAgent succeed");
        return true;
    }

    /**
     * 启动心跳发送定时任务
     * 定期向服务器发送心跳包
     * 
     * @return 启动是否成功
     */
    public boolean sendHeartbeatScheduled() {
        Date date = new Date();
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                try {
                    sendHeartbeat();
                } catch (Exception e) {
                    Log.high.error("sendHeartbeat error," + e);
                }
            }
        }, date, Config.getSystemConfig().getHeartbeatInterval() * Constants.ONE_SECOND_MILLISECONDS);
        return sendHeartbeat();
    }

    /**
     * 发送心跳包
     * 向服务器发送状态信息
     * 
     * @return 发送是否成功
     */
    private boolean sendHeartbeat() {
        try {
            Thread.currentThread().setName("heartBeatThread");
            AddressRequest request = new AddressRequest();
            request.setAddress(Config.getSystemConfig().getMaintainAgent());
            request.setOs(BusinessHelper.getOsType());
            String heartbeatReqJson = Config.getObjectMapper().writeValueAsString(request);
            if (heartbeatReqJson == null || heartbeatReqJson.isEmpty()) {
                Log.high.error("sendRegistry registryReqJson error");
                return false;
            }
            //response

            String heartbeatRespJson = ServerRequestUtil.request(Constants.AGENT_SEND_HEARTBEAT, heartbeatReqJson);
            BaseResponse response = Config.getObjectMapper().readValue(heartbeatRespJson, BaseResponse.class);
            if (response == null || response.getCode() != Constants.SUCCESS_RESPONSE_CODE) {
                Log.low.info("sendHeartbeat error: " + Config.getObjectMapper().writeValueAsString(response));
            } else {
                String serverIp = Config.getSystemConfig().getMaintainServerIp();
                Log.low.info("sendHeartbeat success　" + serverIp);
                Object content = response.getData();
                if (content != null) {
                    String osName = System.getProperty("os.name").toLowerCase();
                    if (osName.contains(Constants.OS_LINUX) && content.toString().equals(Constants.OS_WINDOWS)) {
                        Integer autoNtp = Config.getSystemConfig().getAutoNtp();
                        if (autoNtp == null || 0 == autoNtp) {
                            return true;
                        }
                        String ntpDate = String.format("ntpdate -u %s", serverIp);
                        ProcessUtil.execute(ntpDate, false);
                        String date = ProcessUtil.execute("date", false);
                        if (StringUtils.isNotBlank(date)) {
                            String command = String.format("date -s  \"%s\" ", date);
                            String command2 = "/sbin/hwclock --systohc";
                            ProcessUtil.executeLinux(false, command, command2);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.high.error("sendHeartbeat request error " + e.getMessage(), e);
        } finally {
            try {
                Thread.sleep(Config.getSystemConfig().getHeartbeatInterval());
            } catch (InterruptedException e) {
                Log.high.error(e.getMessage(), e);
            }
        }

        return true;
    }

    /**
     * 检查服务是否存活
     * 
     * @return 服务是否正常运行
     */
    public boolean isServiceAlive() {
        return ServiceStatusHandler.getInstance().isAlive();
    }

    /**
     * 关闭服务
     * 停止所有服务进程
     */
    private void shutService() {
        ServiceStatusHandler.getInstance().setStatus(false);
    }

    /**
     * 启动服务
     * 启动所有必要的服务进程
     */
    private void openService() {
        ServiceStatusHandler.getInstance().setStatus(true);
    }

    /**
     * 获取主机配置文件
     * 
     * @return 配置文件内容
     */
    public String getProfileWithHost() {
        HostProcsStruct struct = operationHelper.getProfileWithHost();
        return ReqRespHelper.writeSuccessWithContent(struct);
    }

    /**
     * 获取主机进程日志
     * 
     * @param proc 进程信息
     * @return 日志内容
     */
    public String getHostProcLogs(HostProcRequest proc) {
        List<LogStruct> content = operationHelper.getHostProcLogs(proc);
        List<LogStruct> collect = null;
        if (ListUtil.isNotEmpty(content)) {
            collect = content.stream().sorted(Comparator.comparing(LogStruct::getTime).reversed()).collect(Collectors.toList());
        }
        return ReqRespHelper.writeSuccessWithContent(collect);
    }

    /**
     * 获取主机进程配置
     * 
     * @param proc 进程信息
     * @return 配置内容
     */
    public String getHostProcConfigs(HostProcRequest proc) {
        List<LogStruct> content = operationHelper.getHostProcConfigs(proc);
        return ReqRespHelper.writeSuccessWithContent(content);
    }

    /**
     * 读取文件内容
     * 
     * @param request 文件操作请求
     * @return 文件内容
     */
    public String readFileToString(OperateFileRequest request) {
        try {
            String content = operationHelper.readFileToString(request);
            return ReqRespHelper.writeSuccessWithContent(content);
        } catch (Exception e) {
            Log.high.error("readFileToString", e);
            return ReqRespHelper.writeResultFailure();
        }
    }

    /**
     * 写入文件内容
     * 
     * @param request 文件操作请求
     * @return 操作结果
     */
    public String writeFile(OperateFileRequest request) {
        try {
            operationHelper.writeFile(request);
            return ReqRespHelper.writeResultSuccess();
        } catch (Exception e) {
            Log.high.error("writeFile", e);
            return ReqRespHelper.writeResultFailure();
        }
    }

    /**
     * 下载主机进程日志
     * 
     * @param proc 进程名
     * @param file 文件名
     * @return 下载结果
     */
    public String downloadHostProcLog(String proc, String file) {
        String link = operationHelper.downloadHostProcLog(proc, file);
        return ReqRespHelper.writeSuccessWithContent(link);
    }

    /**
     * 浏览主机进程日志
     * 
     * @param proc 进程名
     * @param file 文件名
     * @return 日志内容
     */
    public String browseHostProcLog(String proc, String file) {
        String link = operationHelper.browseHostProcLog(proc, file);
        return ReqRespHelper.writeSuccessWithContent(link);
    }

    /**
     * 获取其他存储信息
     * 
     * @param storeDirs 存储目录列表
     * @return 存储信息
     */
    public String getOtherStoreInfo(List<String> storeDirs) {
        HostValueInfoStruct struct = operationHelper.getOtherStoreInfo(storeDirs);
        return ReqRespHelper.writeSuccessWithContent(struct);
    }

    /**
     * 执行单主机时间同步
     * 
     * @param time 目标时间
     * @return 同步结果
     */
    public String execSyncTimeOneHost(long time) {
        boolean execResult = operationHelper.execSyncTimeOneHost(time);
        if (execResult) {
            return ReqRespHelper.writeResultSuccess();
        } else {
            return ReqRespHelper.writeResultFailure();
        }
    }

    /**
     * 获取单主机同步时间
     * 
     * @return 同步时间
     */
    public String getSyncTimeOneHost() {
        SyncTimeStruct struct = operationHelper.getSyncTimeOneHost();
        return ReqRespHelper.writeSuccessWithContent(struct);
    }

    /**
     * 获取预处理磁盘信息
     * 
     * @param params 参数
     * @return 磁盘信息
     */
    public String getPreprocessDiskInfo(String params) {

        FileSystemInfoStruct struct = operationHelper.getPreprocessDiskInfo(params);

        return ReqRespHelper.writeSuccessWithContent(struct);
    }

    /**
     * 更新Logstash配置
     * 
     * @param params 配置参数
     * @return 更新结果
     */
    public String updateLogstashConfig(String params) {

        boolean result = operationHelper.updateLogstashConfig(params);

        return ReqRespHelper.writeSuccessWithContent(result);
    }

    /**
     * 获取系统信息
     * 
     * @param type 信息类型
     * @return 系统信息
     */
    public String getSysInfoOneHost(String type) {
        if ("profile".equals(type)) {
            SysInfoProfileStruct struct = operationHelper.getSysInfoProfileOneHost();
            return ReqRespHelper.writeSuccessWithContent(struct);
        } else if ("detail".equals(type)) {
            SysInfoDetailStruct struct = operationHelper.getSysInfoDetailOneHost();
            return ReqRespHelper.writeSuccessWithContent(struct);
        } else {
            return null;
        }
    }

    /**
     * 获取进程信息
     * 
     * @param requests 进程信息请求列表
     * @return 进程信息
     */
    public String getProcessInfo(List<ProcessInfo> requests) {
        List<Map<String, Object>> mapList = new ArrayList<>();
        try {
            for (ProcessInfo processInfo : requests) {
                Map<String, Object> map = operationHelper.collectProcessInfo(processInfo);
                mapList.add(map);
            }
            return ReqRespHelper.writeSuccessWithContent(mapList);
        } catch (Exception e) {
            Log.high.error("getProcessInfo", e);
        }
        return ReqRespHelper.writeResultFailure();
    }

    /**
     * 获取进程端口信息
     * 
     * @param processInfo 进程信息
     * @return 端口信息
     */
    public String getProcessPortInfo(ProcessInfo processInfo) {
        try {
            List<String> result = operationHelper.collectProcessPortInfo(processInfo);
            return ReqRespHelper.writeSuccessWithContent(result);
        } catch (Exception e) {
            Log.high.error("getProcessPortInfo", e);
        }
        return ReqRespHelper.writeResultFailure();
    }

    /**
     * 调用Python脚本
     * 
     * @param request Python脚本调用请求
     * @return 执行结果
     */
    public String callPythonShell(CallPythonRequest request) {
        if (StringUtils.isEmpty(request.getPythonPath())) {
            request.setPythonPath(Config.getSystemConfig().getPythonPath());
        }
        try {
            String result = operationHelper.callPythonShell(request);
            return ReqRespHelper.writeSuccessWithContent(result);
        } catch (Exception e) {
            Log.high.error("callPythonShell：" + e.getMessage(), e);
            return ReqRespHelper.writeResultFailureWithComtent(e.getMessage());
        }
    }

    /**
     * 获取多个进程的端口信息
     * 
     * @param params 参数
     * @return 端口信息
     */
    public String getProcessPortInfos(String params) {
        Map<String, Object> result = operationHelper.collectProcessPortInfos(params);
        return ReqRespHelper.writeSuccessWithContent(result);
    }

}
