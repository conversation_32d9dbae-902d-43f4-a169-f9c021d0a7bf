package com.maintain.agent.business.thread;

import com.common.log.Log;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2018-01-30
 */
public class StopAgentThread extends BaseThread {
    @Override
    public void run() {
        //延时执行
        try {
            TimeUnit.MILLISECONDS.sleep(1000);
        } catch (InterruptedException e) {
            Log.high.error("closeServiceAgent error", e);
        }

        //关闭整个Server端进程
        System.exit(0);
    }
}
