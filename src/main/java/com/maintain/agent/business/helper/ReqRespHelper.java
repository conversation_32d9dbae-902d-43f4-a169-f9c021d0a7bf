package com.maintain.agent.business.helper;

import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.response.BaseResponse;
import com.common.log.Log;
import com.fasterxml.jackson.core.JsonProcessingException;

/**
 * 请求响应辅助类
 * 提供统一的响应结果生成方法，包括：
 * 1. 成功响应
 * 2. 失败响应
 * 3. 参数错误响应
 * 4. 权限错误响应
 * 5. 服务不可用响应
 * 
 * <AUTHOR>
 * @date 2016/11/1
 */
public class ReqRespHelper {

    /**
     * 私有构造函数，防止实例化
     */
    private ReqRespHelper() {
    }

    /**
     * 生成操作成功响应
     * 返回标准成功响应，不包含额外数据
     * 
     * @return 成功响应的JSON字符串
     */
    public static String writeResultSuccess() {
        return writeResult(Constants.SUCCESS_RESPONSE_CODE, Constants.SUCCESS, null, new BaseResponse());
    }

    /**
     * 生成操作失败响应
     * 返回标准失败响应，不包含额外数据
     * 
     * @return 失败响应的JSON字符串
     */
    public static String writeResultFailure() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.FAILURE, null, new BaseResponse());
    }

    /**
     * 生成带提示信息的失败响应
     * 
     * @param content 失败提示信息
     * @return 失败响应的JSON字符串
     */
    public static String writeResultFailureWithComtent(String content) {
        return writeResult(Constants.FAIL_RESPONSE_CODE, content, null, new BaseResponse());
    }

    /**
     * 生成参数为空响应
     * 
     * @return 参数为空响应的JSON字符串
     */
    public static String writeParamsEmpty() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.PARAMS_EMPTY, null, new BaseResponse());
    }

    /**
     * 生成参数不合法响应
     * 
     * @return 参数不合法响应的JSON字符串
     */
    public static String writeParamsIllegal() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.PARAMS_ILLEGAL, null, new BaseResponse());
    }

    /**
     * 生成参数错误响应
     * 
     * @return 参数错误响应的JSON字符串
     */
    public static String writeParamsError() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.PARAMS_ERROR, null, new BaseResponse());
    }

    /**
     * 生成拒绝访问响应
     * 
     * @return 拒绝访问响应的JSON字符串
     */
    public static String writeAccessDenied() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.ACCESS_DENIED, null, new BaseResponse());
    }

    /**
     * 生成未知请求响应
     * 
     * @return 未知请求响应的JSON字符串
     */
    public static String writeUnknownRequest() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.UNKNOWN_REQUEST, null, new BaseResponse());
    }

    /**
     * 生成服务不可用响应
     * 
     * @return 服务不可用响应的JSON字符串
     */
    public static String writeNoServiceAvail() {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.NO_SERVICE_AVAILABLE, null, new BaseResponse());
    }

    /**
     * 生成带数据的成功响应
     * 
     * @param content 响应数据对象
     * @return 成功响应的JSON字符串
     */
    public static String writeSuccessWithContent(Object content) {
        return writeResult(Constants.SUCCESS_RESPONSE_CODE, Constants.SUCCESS, content, new BaseResponse());
    }

    /**
     * 生成带数据的失败响应
     * 
     * @param content 响应数据对象
     * @return 失败响应的JSON字符串
     */
    public static String writeResultFailureWithContent(Object content) {
        return writeResult(Constants.FAIL_RESPONSE_CODE, Constants.FAILURE, content, new BaseResponse());
    }

    /**
     * 生成通用响应结果
     * 将响应信息转换为JSON字符串
     * 
     * @param status 响应状态码
     * @param message 响应消息
     * @param content 响应数据
     * @param response 响应对象
     * @return 响应结果的JSON字符串
     */
    public static String writeResult(int status, String message, Object content, BaseResponse response) {
        response.setCode(status);
        response.setMsg(message);
        response.setData(content);
        try {
            return Config.getObjectMapper().writeValueAsString(response);
        } catch (JsonProcessingException e) {
            Log.high.error("writeResult error", e);
            return "{\"code\":\"" + status + "\",\"msg\":\"" + message + "\",\"data\":\"" + content + "\"}";
        }
    }
}
