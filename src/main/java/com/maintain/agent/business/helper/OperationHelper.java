package com.maintain.agent.business.helper;

import com.maintain.agent.business.service.HardInfo;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.request.CallPythonRequest;
import com.maintain.agent.entity.request.HostProcRequest;
import com.maintain.agent.entity.request.OperateFileRequest;
import com.maintain.agent.entity.request.ProcessInfo;
import com.maintain.agent.entity.struct.*;
import com.maintain.agent.enums.OsTypeEnum;
import com.maintain.agent.server.SoftwareMonitor;
import com.common.log.Log;
import com.fasterxml.jackson.core.type.TypeReference;
import com.maintain.agent.utils.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 操作辅助类
 * 提供各种系统操作相关的辅助功能，包括：
 * 1. 进程管理（进程信息获取、状态监控等）
 * 2. 文件操作（读取、写入、下载等）
 * 3. 配置管理（配置文件读取、更新等）
 * 4. 系统信息收集（磁盘、进程等）
 * 5. Python脚本调用
 * 
 * <AUTHOR>
 * @date 2016/11/2
 */
public class OperationHelper {
    /**
     * Ice服务映射表
     * 存储服务名称到服务目录的映射关系
     */
    private Map<String, String> iceServiceMap;

    /**
     * Agent工作目录
     * 运维系统Agent端的工作目录路径
     */
    private String agentWorkDir = Config.getSystemConfig().getWorkDirName();

    /**
     * 获取主机进程概要信息
     * 返回所有Ice服务的名称列表
     * 
     * @return 包含主机IP和进程列表的结构体
     */
    public HostProcsStruct getProfileWithHost() {
        if (iceServiceMap == null) {
            return null;
        }
        HostProcsStruct struct = new HostProcsStruct();
        struct.setHost(HardInfo.getInstance().getIp());
        struct.setProcs(DataTransUtil.toStrList(iceServiceMap.keySet()));
        return struct;
    }

    /**
     * 获取进程日志信息
     * 
     * @param proc 进程信息请求对象
     * @return 日志文件信息列表
     */
    public List<LogStruct> getHostProcLogs(HostProcRequest proc) {
        return BusinessHelper.getAllLogs(proc);
    }

    /**
     * 获取进程配置信息
     * 
     * @param proc 进程信息请求对象
     * @return 配置文件信息列表
     */
    public List<LogStruct> getHostProcConfigs(HostProcRequest proc) {
        return BusinessHelper.getAllConfigs(proc);
    }

    /**
     * 读取文件内容
     * 读取指定进程目录下的文件内容
     * 
     * @param request 文件操作请求对象
     * @return 文件内容的Base64编码字符串
     */
    public String readFileToString(OperateFileRequest request) {
        String filePath = getProcFilePath(request);
        return readFile(filePath);
    }

    /**
     * 写入文件内容
     * 将内容写入指定进程目录下的文件
     * 
     * @param request 文件操作请求对象
     * @throws UnsupportedEncodingException 当编码不支持时抛出
     */
    public void writeFile(OperateFileRequest request) throws UnsupportedEncodingException {
        String filePath = getProcFilePath(request);
        writeContentToFile(filePath, request.getContent());
    }

    /**
     * 获取进程文件路径
     * 根据操作系统类型和配置构建完整的文件路径
     * 
     * @param request 文件操作请求对象
     * @return 完整的文件路径
     */
    private String getProcFilePath(OperateFileRequest request) {
        int osId = BusinessHelper.getOsType();
        final String isBackupFile = request.getBackupFile();
        final String name = request.getProc();
        final SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(name);
        String filePath;
        String tempPath;
        if (osId == OsTypeEnum.LINUX.getType()) {
            if (softwareInfo != null) {
                if ("false".equals(isBackupFile)) {
                    tempPath =  softwareInfo.getRealDir();
                    if (!tempPath.endsWith(File.separator)){
                        tempPath = tempPath + File.separator;
                    }
                } else {
                    tempPath = softwareInfo.getRealDir().replace(name,"") + "bak/" + request.getVersion() + "/" + request.getProc() + "_bak/";
                }
            } else {
                if ("false".equals(isBackupFile)) {
                    tempPath =  "/dist/" + request.getProc() + "/";
                } else {
                    tempPath = "/dist/bak/" + request.getVersion() + "/" + request.getProc() + "_bak/";
                }
            }
            if (StringUtils.isEmpty(request.getConfigPath())) {
                if (StringUtils.isEmpty(request.getPath())) {
                    filePath = tempPath + request.getFile();
                } else {
                    filePath = tempPath + request.getPath() + "/" + request.getFile();
                }
            } else {
                if (request.getConfigPath().startsWith("/")) {
                    filePath = request.getConfigPath() + File.separator + request.getFile();
                } else {
                    filePath = tempPath + request.getConfigPath() + File.separator + request.getFile();
                }
            }
        } else if (osId == OsTypeEnum.WINDOWS.getType()) {
            if (softwareInfo != null) {
                if ("false".equals(isBackupFile)) {
                    tempPath =  softwareInfo.getRealDir() + "\\";
                } else {
                    tempPath = softwareInfo.getRealDir().replace(name,"") + "bak\\" + request.getVersion() + "\\" + request.getProc() + "_bak\\";
                }
            } else {
                if ("false".equals(isBackupFile)) {
                    tempPath =  "D:\\dist\\" + request.getProc() + "\\";
                } else {
                    tempPath = "D:\\dist\\bak\\" + request.getVersion() + "\\" + request.getProc() + "_bak\\";
                }
            }
            if (StringUtils.isEmpty(request.getPath())) {
                filePath = tempPath + request.getFile();
            } else {
                filePath = tempPath + request.getPath() + "\\" + request.getFile();
            }
        } else {
            throw new RuntimeException("未知操作系统");
        }
        //读取配置目录下的文件，因为配置目录可能不全为conf
        if (!new File(filePath).exists() && "conf".equals(request.getPath())) {
            for (String configPath : Config.getSystemConfig().getConfigPathList()) {
                if (configPath.equals(request.getPath())) {
                    continue;
                }
                String path = filePath.replaceFirst(request.getPath(), configPath);
                if (new File(path).exists()) {
                    return path;
                }
            }
        }
        return filePath;
    }

    /**
     * 写入文件内容
     * 将Base64解码后的内容写入指定文件
     * 
     * @param path 文件路径
     * @param content Base64编码的内容
     * @throws UnsupportedEncodingException 当编码不支持时抛出
     */
    private void writeContentToFile(String path, String content) throws UnsupportedEncodingException {
        File file = new File(path);
        if (!file.exists()) {
            throw new RuntimeException(path + "不存在");
        }
        byte[] bytes = Base64.getDecoder().decode(content);
        content = new String(bytes, Charset.forName(Constants.ENCODE_UTF8)).trim();
        //使用字节流输出会导致文件尾部出现多余符号，notepad++中出现NUL字样,原因未知
        try (BufferedWriter bw = new BufferedWriter(new OutputStreamWriter(
                new FileOutputStream(file), Constants.ENCODE_UTF8))) {
            bw.write(content);
            bw.flush();
        } catch (IOException e) {
            throw new RuntimeException("回写文件失败", e);
        }
    }

    /**
     * 读取文件内容
     * 读取文件内容并进行Base64编码
     * 
     * @param path 文件路径
     * @return Base64编码的文件内容
     */
    private String readFile(String path) {
        File file = new File(path);
        if (!file.exists()) {
            return Base64.getEncoder().encodeToString("文件不存在!".getBytes());
        }
        try {
            String content = FileUtils.readFileToString(file, Constants.ENCODE_UTF8);
            byte[] bytes = content.getBytes(Charset.forName(Constants.ENCODE_UTF8));
            return Base64.getEncoder().encodeToString(bytes);
        } catch (IOException e) {
            try (ByteArrayOutputStream baos = new ByteArrayOutputStream();
                 FileInputStream fis = new FileInputStream(file)) {
                byte[] buffer = new byte[1024];
                while ((fis.read(buffer)) != -1) {
                    baos.write(buffer);
                }
                String content = new String(baos.toByteArray(), Charset.forName(Constants.ENCODE_UTF8)).trim();
                byte[] bytes = content.getBytes(Charset.forName(Constants.ENCODE_UTF8));
                return Base64.getEncoder().encodeToString(bytes);
            } catch (IOException e1) {
                throw new RuntimeException("读取文件失败", e1);
            }

        }
        /* */
    }

    /**
     * 下载进程日志
     * 
     * @param proc 进程名
     * @param file 文件名
     * @return 下载链接
     */
    public String downloadHostProcLog(String proc, String file) {
        String serviceDir;
        if (iceServiceMap != null && iceServiceMap.containsKey(proc)) {
            serviceDir = iceServiceMap.get(proc);
        } else if (agentWorkDir != null && agentWorkDir.contains(proc)) {
            serviceDir = agentWorkDir;
        } else {
            return null;
        }
        return BusinessHelper.copyFileToFtpPerm(proc, serviceDir, file);
    }

    /**
     * 浏览进程日志
     * 
     * @param proc 进程名
     * @param file 文件名
     * @return 日志内容
     */
    public String browseHostProcLog(String proc, String file) {
        String serviceDir;
        if (iceServiceMap != null && iceServiceMap.containsKey(proc)) {
            serviceDir = iceServiceMap.get(proc);
        } else if (agentWorkDir != null && agentWorkDir.contains(proc)) {
            serviceDir = agentWorkDir;
        } else {
            return null;
        }
        return BusinessHelper.copyFileToFtpTemp(proc, serviceDir, file);
    }

    /**
     * 获取其他存储信息
     * 
     * @param storeDirs 存储目录列表
     * @return 存储信息结构体
     */
    public HostValueInfoStruct getOtherStoreInfo(List<String> storeDirs) {
        return BusinessHelper.getStoreInfo(storeDirs);
    }

    /**
     * 执行单主机时间同步
     * 
     * @param time 目标时间戳
     * @return 同步是否成功
     */
    public boolean execSyncTimeOneHost(long time) {
        return BusinessHelper.execSyncTime(time);
    }

    /**
     * 获取单主机时间同步信息
     * 
     * @return 时间同步信息结构体
     */
    public SyncTimeStruct getSyncTimeOneHost() {
        return BusinessHelper.getSyncTimeOneHost();
    }

    /**
     * 获取系统概要信息
     * 
     * @return 系统概要信息结构体
     */
    public SysInfoProfileStruct getSysInfoProfileOneHost() {
        return BusinessHelper.getSysInfoProfileOneHost();
    }

    /**
     * 获取系统详细信息
     * 
     * @return 系统详细信息结构体
     */
    public SysInfoDetailStruct getSysInfoDetailOneHost() {
        return BusinessHelper.getSysInfoDetailOneHost();
    }

    /**
     * 更新缓存和映射
     * 更新Ice服务映射表
     * 
     * @return 更新是否成功
     */
    public boolean updateCacheAndMap() {
        List<String[]> serviceList = BusinessHelper.findIceService();
        if (serviceList != null && !serviceList.isEmpty()) {
            //设置服务与工作目录的映射，留作其他方法直接取用
            iceServiceMap = DataTransUtil.toStrMap(serviceList);
        }
        return true;
    }

    /**
     * 收集进程信息
     * 
     * @param processInfo 进程信息请求对象
     * @return 进程详细信息
     * @throws Exception 当收集过程出现异常时抛出
     */
    public Map<String, Object> collectProcessInfo(ProcessInfo processInfo) throws Exception {
        String process = processInfo.getName();
        int pid = processInfo.getPid();
        int portCount;
        long errorFileSize;
        Map<String, Object> map = new HashMap<>();
        if (process == null || pid == 0) {
            map.put("portCount", 0);
            map.put("errorFileSize", 0);
            map.put("name", process);
            return map;
        }
        int osId = BusinessHelper.getOsType();
        final SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(process);
        if (osId == OsTypeEnum.LINUX.getType()) {
            String command = "netstat -anp | grep " + pid + " | grep tcp | wc -l";
            //Log.low.debug("exec command: " + command);
            String[] result = ExecUtil.execCmdOnLinux(command);
            //Log.low.debug("exec result:" + Arrays.toString(result));
            portCount = Integer.parseInt(result[0].trim());
            if (StringUtils.isNotEmpty(result[1])) {
                Log.high.error("exec " + command + " has error:" + result[1]);
            }
            String logPath;
            if (softwareInfo != null) {
                logPath = softwareInfo.getRealDir() + File.separator + "log";
            } else {
                logPath = "/dist/" + process + "/log";
            }
            errorFileSize = calcFileSize(logPath);
        } else {
            String command = "netstat -ano | find /i \"" + pid + "\" /c";
            String[] result = ExecUtil.execCmdOnWindows(command);
            //Log.low.debug("exec result:" + Arrays.toString(result));
            portCount = Integer.parseInt(result[0].trim());
            if (StringUtils.isNotEmpty(result[1])) {
                Log.high.error("exec " + command + " has error:" + result[1]);
            }
            String logPath;
            if (softwareInfo != null) {
                logPath = softwareInfo.getRealDir() + File.separator + "log";
            } else {
                logPath = "D:\\dist\\" + process + "\\log";
            }
            errorFileSize = calcFileSize(logPath);
        }
        map.put("portCount", portCount);
        map.put("errorFileSize", errorFileSize);
        map.put("name", process);
        return map;
    }


    /**
     * 获取预处理磁盘信息
     * 
     * @param preprocessDir 预处理目录
     * @return 文件系统信息结构体
     */
    public static FileSystemInfoStruct getPreprocessDiskInfo(String preprocessDir) {
        File file = new File(preprocessDir);
        if (!file.exists() || file.isFile()) {
            return null;
        }
        long dirDisk = FileUtil.sizeOfDirectory(file);
        long total = file.getTotalSpace();
        long free = file.getFreeSpace();
        long used = total - free;
        return new FileSystemInfoStruct(preprocessDir, total, free, used, dirDisk);
    }


    /**
     * 更新Logstash配置
     * 
     * @param config 配置内容
     * @return 更新是否成功
     */
    public static boolean updateLogstashConfig(String config) {
        if (StringUtils.isBlank(config)) {
            return false;
        }
        //查询本地操作系统是否存在logstash启动配置文件
        if(ProcessUtil.isWindows()){
            File configFile = new File("d:\\dist\\logstash\\bin\\ywxtes.conf");
            if(!configFile.exists()) return false;
            try {
                FileUtil.writeStringToFile(configFile,config,Constants.ENCODE_UTF8,false);
                return true;
            } catch (IOException e) {
                Log.high.error(e);
            }
        }else{
            File configFile = new File("/dist/logstash/bin/ywxtes.conf");
            if(!configFile.exists()) return false;
            try {
                FileUtil.writeStringToFile(configFile,config,Constants.ENCODE_UTF8,false);
                return true;
            } catch (IOException e) {
                Log.high.error(e);
            }
        }
        return false;
    }

    /**
     * 计算文件大小
     * 
     * @param logPath 日志文件路径
     * @return 文件大小
     */
    private long calcFileSize(String logPath) {
        long errorFileSize = 0L;
        File file = new File(logPath);
        File[] files = file.listFiles(f -> f.getName().contains("error"));
        if (files != null) {
            for (File f : files) {
                errorFileSize += f.length();
            }
        }
        return errorFileSize;
    }

    /**
     * 收集进程端口信息
     * 
     * @param processInfo 进程信息请求对象
     * @return 端口信息列表
     * @throws Exception 当收集过程出现异常时抛出
     */
    public List<String> collectProcessPortInfo(ProcessInfo processInfo) throws Exception {
        int osId = BusinessHelper.getOsType();
        if (osId == OsTypeEnum.LINUX.getType()) {
            String command = "netstat -anp | grep " + processInfo.getPid();
            String[] result = ExecUtil.execCmdOnLinux(command);
            if (StringUtils.isNotEmpty(result[1])) {
                Log.high.error("exec " + command + " has error:" + result[1]);
            }
            return convertStringToList(result[0], "\r\n");
        } else {
            String command = "netstat -ano | findstr \"" + processInfo.getPid() + "\"";
            String[] result = ExecUtil.execCmdOnWindows(command);
            if (StringUtils.isNotEmpty(result[1])) {
                Log.high.error("exec " + command + " has error:" + result[1]);
            }
            return convertStringToList(result[0], "\r\n");
        }
    }

    /**
     * 将字符串转换为列表
     * 
     * @param s 输入字符串
     * @param pattern 分隔符
     * @return 字符串列表
     */
    private List<String> convertStringToList(String s, String pattern) {
        List<String> list = new ArrayList<>();
        if (StringUtils.isNotEmpty(s)) {
            list = Arrays.stream(s.split(pattern)).map(String::trim).collect(Collectors.toList());
        }
        return list;
    }


    /**
     * 调用Python脚本
     * 
     * @param request Python脚本调用请求对象
     * @return 执行结果
     * @throws Exception 当执行过程出现异常时抛出
     */
    public String callPythonShell(CallPythonRequest request) throws Exception {
        String param = request.getParam();
        param = Config.getObjectMapper().writeValueAsString(param);
        if (ProcessUtil.isWindows()) {
            param = param.replaceAll("&", "^&");
        }
        int osId = BusinessHelper.getOsType();
        StringBuilder command = new StringBuilder();
        command.append("python ").append(request.getShellPath())
                .append(" ").append(param);
        String[] result = null;
        if (osId == OsTypeEnum.LINUX.getType()) {
            result = ExecUtil.execCmdOnLinux(command.toString());
        } else if (osId == OsTypeEnum.WINDOWS.getType()) {
            try {
                result = ExecUtil.execCmdOnWindows(command.toString());
            } catch (Exception e) {
                //WINDOWS上安装了anaconda3集成环境后，能在cmd中识别python命令，但通过远程调用失败
                if (e.getMessage().contains("Cannot run program \"python\"")) {
                    command.insert(0, request.getPythonPath() + " ");
                    result = ExecUtil.execCmdOnWindows(command.toString());
                } else {
                    throw e;
                }
            }
        }
        if (result == null) {
            return null;
        }
        if (result != null && StringUtils.isNotEmpty(result[1])) {
            throw new RuntimeException(result[1]);
        }
        String response = result[0];
        if (StringUtils.isNotEmpty(response) && response.contains("success")) {
            return "操作成功";
        }
        return null;
    }


    /**
     * 收集多个进程的端口信息
     * 
     * @param params 参数
     * @return 端口信息映射
     */
    public Map<String, Object> collectProcessPortInfos(String params) {
        try {
            List<Map<Integer, Integer>> list = Config.getObjectMapper().readValue(params, new TypeReference<List<Map<Integer, Integer>>>() {
            });
            Map<String, Object> result = new HashMap<>();
            for (Map<Integer, Integer> map : list) {
                for (Map.Entry<Integer, Integer> entry : map.entrySet()) {
                    String softwareId = entry.getKey().toString();
                    Integer pid = entry.getValue();
                    ProcessInfo processInfo = new ProcessInfo();
                    processInfo.setPid(pid);
                    List<String> portList = collectProcessPortInfo(processInfo);
                    if (ListUtil.isNotEmpty(portList)) {
                        result.put(softwareId, portList);
                    } else {
                        if (!ProcessUtil.isWindows()) {
                            //Linux下进程信息还是有的
                            String path = "/proc/" + pid;
                            File file = new File(path);
                            if (file.exists()) {
                                result.put(softwareId, new ArrayList<>());
                            } else {
                                result.put(softwareId, null);
                            }
                        }

                    }
                }
            }
            return result;
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
        return null;
    }
}
