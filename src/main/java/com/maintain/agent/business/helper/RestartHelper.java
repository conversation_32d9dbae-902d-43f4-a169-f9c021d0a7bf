package com.maintain.agent.business.helper;

import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.struct.RestartStruct;
import com.maintain.agent.utils.ProcessUtil;
import com.common.log.Log;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * 进程重启辅助类
 * 提供进程重启相关的功能，包括：
 * 1. 进程重启
 * 2. 进程假死检测
 * 3. 日志分析
 * 
 * <AUTHOR>
 * @date 2018-02-28
 */
public class RestartHelper {

    /**
     * 重启指定进程列表中的进程
     * 步骤：
     * 1. 检查进程工作目录是否存在
     * 2. 获取进程PID
     * 3. 终止进程
     * 
     * @param list 需要重启的进程信息列表
     */
    public static void restartProcess(List<RestartStruct> list) {
        if (list == null || list.isEmpty()) {
            return;
        }

        List<String> keys = new ArrayList<>();

        for (RestartStruct struct : list) {
            File file = new File(struct.getWorkDir() + "\\deploy");
            if (file.exists()) {
                keys.add(struct.getKey());
            }
        }
        List<Long> pids = ProcessUtil.getPidByParam(keys);
        if (pids == null || pids.isEmpty()) {
            Log.low.info("there is no pid to kill");
            return;
        }
        for (long pid : pids) {
            if (ProcessUtil.killProcess(pid)) {
                Log.low.info("kill process success, pid=" + pid);
            } else {
                Log.high.error("kill process fail. pid=" + pid);
            }
        }
    }

    /**
     * 获取需要重启的进程列表
     * 判断条件：
     * 1. 日志文件最后修改时间超过阈值
     * 2. 日志文件中连续出现指定字符串的次数超过阈值
     * 
     * @return 需要重启的进程列表
     */
    public static List<RestartStruct> getNeedRestartProcess() {
        List<RestartStruct> list = Config.getRestartConfig().getProcesses();
        if (list == null || list.isEmpty()) {
            return new ArrayList<>();
        }

        List<RestartStruct> restartList = new ArrayList<>();

        for (RestartStruct struct : list) {
            String logPath = struct.getWorkDir() + "\\log\\log.txt";
            File logFile = new File(logPath);
            if (!logFile.exists()) {
                continue;
            }

            long per = System.currentTimeMillis() - logFile.lastModified();

            if (per > struct.getDeadTimeMin() * Constants.ONE_MINUTE_MILLISECONDS) {
                restartList.add(struct);
                continue;
            }
            String judgeValue = struct.getJudgeValue();
            int times = struct.getTimes();
            if (!judgeValue.isEmpty()) {
                if (judgeOneLog(logFile, "UTF-8", judgeValue, times)) {
                    restartList.add(struct);
                }
            }
        }

        StringBuilder stringBuilder = new StringBuilder();
        for (int i = 0; i < restartList.size(); i++) {
            stringBuilder.append(restartList.get(i).getKey());
            if (i != restartList.size() - 1) {
                stringBuilder.append(", ");
            }
        }
        Log.low.info("need restart process:" + stringBuilder.toString());

        return restartList;
    }

    /**
     * 判断日志文件是否出现假死
     * 通过检查日志文件末尾是否连续出现指定字符串来判断
     * 
     * @param logFile 日志文件
     * @param encode 文件编码
     * @param judgeValue 需要检查的字符串
     * @param timeThreashold 连续出现的次数阈值
     * @return 如果判断为假死返回true，否则返回false
     */
    public static boolean judgeOneLog(File logFile, String encode, String judgeValue, int timeThreashold) {
        try (FileInputStream inputStream = new FileInputStream(logFile);
             InputStreamReader inputStreamReader = new InputStreamReader(inputStream, encode);
             BufferedReader reader = new BufferedReader(inputStreamReader)) {
            String line = "";
            int lineNum = 0;
            List<Integer> numList = new ArrayList<>();
            while ((line = reader.readLine()) != null) {
                lineNum++;
                if (line.contains(judgeValue)) {
                    numList.add(lineNum);
                }
            }
            if (numList.size() < timeThreashold) {
                return false;
            } else {
                for (int i = lineNum; i > lineNum - timeThreashold; i--) {
                    if (!numList.contains(i)) {
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            Log.high.error("judgeOneLog error, logPath=" + logFile.getAbsolutePath());
            return false;
        }
    }

}
