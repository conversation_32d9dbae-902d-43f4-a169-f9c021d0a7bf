package com.maintain.agent.business.helper;

import com.common.log.Log;
import com.maintain.agent.business.service.HardInfo;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.request.HostProcRequest;
import com.maintain.agent.entity.struct.*;
import com.maintain.agent.enums.OsTypeEnum;
import com.maintain.agent.utils.*;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.hyperic.sigar.*;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 业务辅助类
 * 提供各种业务相关的辅助功能，包括：
 * 1. 文件系统操作（目录大小计算、文件信息获取等）
 * 2. 进程管理（Ice服务进程查找、状态检查等）
 * 3. 系统信息收集（CPU、内存、文件系统等）
 * 4. 日志和配置文件管理
 * 5. 时间同步
 * 
 * <AUTHOR>
 * @date 2016/11/4
 */
public class BusinessHelper {

    /**
     * 文件路径分隔符
     */
    private static final String separator = System.getProperty("file.separator");

    /**
     * 获取存储目录信息
     * 计算指定目录列表的总大小和详细信息
     * 
     * @param storeDirs 存储目录列表
     * @return 包含目录大小和详细信息的结构体
     */
    public static HostValueInfoStruct getStoreInfo(List<String> storeDirs) {
        String details = "";
        StringBuilder temp = new StringBuilder();
        for (String storeDir : storeDirs) {
            long size = 0;
            try {
                size = FileUtil.getDirSize(new File(storeDir));
            } catch (FileNotFoundException e) {
                Log.high.error("dir not exists, path=" + storeDir);
                continue;
            }
            //目录的大小
            temp.append("[dir:").append(storeDir).append(", used:")
                    .append(DataTransUtil.toBytesStrFormat(size))
                    .append("]").append(Constants.NEW_LINE_IN_HTML);
        }

        if (temp.length() > Constants.NEW_LINE_IN_HTML.length()) {
            //去除末尾的换行符
            details = temp.substring(0, temp.length() - Constants.NEW_LINE_IN_HTML.length());
        }
        HostValueInfoStruct struct = new HostValueInfoStruct();
        struct.setHost(HardInfo.getInstance().getIp());
        struct.setInfo(details);
        return struct;
    }

    /**
     * 查找所有Ice服务相关进程
     * 搜索工作目录下的所有Ice服务进程，返回服务名称和路径信息
     * 
     * @return Ice服务信息列表，每个元素包含服务名称和路径
     */
    public static List<String[]> findIceService() {
        List<String> workDirs = getAllWorkDirs();
        if (workDirs == null || workDirs.isEmpty()) {
            return null;
        }
        List<String[]> procsFound = new LinkedList<>();
        for (String workDir : workDirs) {
            if (!BusinessCheckUtil.checkUserProc(workDir)) {
                continue;
            }
            String[] nameDir = BusinessCheckUtil.getIceService(workDir);
            if (nameDir == null || nameDir.length != 2) {
                continue;
            }
            procsFound.add(nameDir);
        }
        return procsFound;
    }

    /**
     * 获取所有工作目录
     * 根据操作系统类型获取默认工作目录下的所有子目录
     * 
     * @return 工作目录路径列表
     */
    private static List<String> getAllWorkDirs() {
        //dir
        String dirStr = null;
        if (HardInfo.isLinux()) {
            dirStr = "/dist";
        } else if (HardInfo.isWindows()) {
            dirStr = "D:\\dist";
        } else {
            Log.high.error("getAllWorkDirs unsupported OS type");
            return null;
        }
        File dir = new File(dirStr);
        if (!dir.exists() || !dir.isDirectory()) {
            Log.high.error("getAllWorkDirs not exist [dir:" + dirStr + "]");
            return null;
        }
        //allWorkDirs
        File[] files = dir.listFiles();
        if (files == null || files.length == 0) {
            Log.high.error("getAllWorkDirs no files under [dir:" + dirStr + "]");
            return null;
        }
        List<String> allWorkDirs = new LinkedList<>();
        for (File file : files) {
            if (!file.exists() || !file.isDirectory()) {
                continue;
            }
            allWorkDirs.add(file.getPath());
        }
        return allWorkDirs;
    }

    /**
     * 获取指定目录下的所有日志文件信息
     * 支持多个日志目录和文件后缀过滤
     * 
     * @param proc 包含服务路径和日志配置的请求对象
     * @return 日志文件信息列表
     */
    public static List<LogStruct> getAllLogs(HostProcRequest proc) {
        String serviceDir = proc.getPath();
        List<LogStruct> logStructList = new LinkedList<>();
        if (StringUtils.isEmpty(proc.getLogPath())) {
            List<FileInfo> fileInfoList = getFileInfoList(serviceDir + File.separator + "log");
            List<FileInfo> fileInfos = getFileInfoList(serviceDir + File.separator + "logs");
            if (fileInfoList != null) {
                for (FileInfo fileInfo : fileInfoList) {
                    logStructList.add(copyToLogStruct(fileInfo));
                }
            }
            if (fileInfos != null) {
                for (FileInfo fileInfo : fileInfos) {
                    logStructList.add(copyToLogStruct(fileInfo));
                }
            }
        } else {
            List<FileInfo> fileInfoList;
            //绝对路径
            if (proc.getLogPath().startsWith("/")) {
                fileInfoList = getFileInfoList(proc.getLogPath());
            } else {
                fileInfoList = getFileInfoList(serviceDir + File.separator + proc.getLogPath());
            }
            if (fileInfoList != null) {
                fileInfoList.stream()
                        .map(BusinessHelper::copyToLogStruct)
                        .forEach(logStructList::add);
            }
        }
        if (StringUtils.isEmpty(proc.getLogSuffix())) {
            return logStructList;
        }
        return logStructList.stream()
                .filter(l -> l.getName().endsWith(proc.getLogSuffix()))
                .collect(Collectors.toList());
    }

    /**
     * 获取指定目录下的所有配置文件信息
     * 支持多个配置目录和自定义配置路径
     * 
     * @param proc 包含服务路径和配置路径的请求对象
     * @return 配置文件信息列表
     */
    public static List<LogStruct> getAllConfigs(HostProcRequest proc) {
        String serviceDir = proc.getPath();
        if (StringUtils.isEmpty(proc.getConfigPath())) {
            for (String configPath : Config.getSystemConfig().getConfigPathList()) {
                List<FileInfo> fileInfoList = getFileInfoList(serviceDir + File.separator + configPath);
                if (fileInfoList == null || fileInfoList.isEmpty()) {
                    continue;
                }
                return fileInfoList.stream().map(BusinessHelper::copyToLogStruct).
                        collect(Collectors.toCollection(LinkedList::new));
            }
        } else {
            List<FileInfo> fileInfoList;
            if (proc.getConfigPath().startsWith("/")) {
                fileInfoList = getFileInfoList(proc.getConfigPath());
            } else {
                fileInfoList = getFileInfoList(proc.getPath() + File.separator + proc.getConfigPath());
            }
            if (CollectionUtils.isEmpty(fileInfoList)) {
                return Collections.EMPTY_LIST;
            }
            return fileInfoList.stream().map(BusinessHelper::copyToLogStruct).
                    collect(Collectors.toCollection(LinkedList::new));
        }
        return Collections.EMPTY_LIST;
    }

    /**
     * 将文件复制到FTP永久目录
     * 
     * @param serviceName 服务名称
     * @param serviceDir 服务目录
     * @param fileName 文件名
     * @return 复制后的文件路径
     */
    public static String copyFileToFtpPerm(String serviceName, String serviceDir, String fileName) {
        String sep = File.separator;
        File srcFile = new File(serviceDir + sep + "log" + sep + fileName);
        if (!srcFile.exists() || !srcFile.isFile()) {
            return null;
        }
        String dateFormat = DateUtil.formatDate(System.currentTimeMillis(), "yyyyMMdd");
        String ftpSuffix = dateFormat + sep + serviceName + sep + "perm" + sep + fileName;
        String ftpLink = Config.getSystemConfig().getFtpPrefix() + ":" + Config.getSystemConfig().getFtpPort() + sep + ftpSuffix;

        File copyFile = new File(Config.getSystemConfig().getFtpDir() + sep + ftpSuffix);
        if (copyFile.exists() && copyFile.isFile()) {
            /*FileInfo copyFileInfo = getFileInfo(copyFile.getPath());
            if (copyFileInfo != null && System.currentTimeMillis() - copyFileInfo.getCtime() <= Constants.ONE_MINUTE_MILLISECONDS) {
                //存在最近一分钟内的同名日志文件，就返回；避免频繁拷贝文件
                return ftpLink;
            }*/
            //需要重新拷贝新的同名日志文件；删除同名日志文件
            FileUtils.deleteQuietly(copyFile);
        }
        try {
            FileUtils.copyFile(srcFile, copyFile);
        } catch (IOException e) {
            Log.high.error("copyFileToFtpPerm [serviceDir:" + serviceDir + ", fileName:" + fileName + "] error", e);
            return null;
        }
        return ftpLink;
    }

    /**
     * 将文件复制到FTP临时目录
     * 
     * @param serviceName 服务名称
     * @param serviceDir 服务目录
     * @param fileName 文件名
     * @return 复制后的文件路径
     */
    public static String copyFileToFtpTemp(String serviceName, String serviceDir, String fileName) {
        String sep = File.separator;
        File srcFile = new File(serviceDir + sep + "log" + sep + fileName);
        if (!srcFile.exists() || !srcFile.isFile()) {
            return null;
        }
        String dateFormat = DateUtil.formatDate(System.currentTimeMillis(), "yyyyMMdd");
        String ftpSuffix = dateFormat + sep + serviceName + sep + "temp" + sep + fileName;
        String ftpLink = Config.getSystemConfig().getFtpPrefix() + ":" + Config.getSystemConfig().getFtpPort() + sep + ftpSuffix;

        File copyFile = new File(Config.getSystemConfig().getFtpDir() + sep + ftpSuffix);
        if (copyFile.exists() && copyFile.isFile()) {
            FileInfo copyFileInfo = getFileInfo(copyFile.getPath());
            if (copyFileInfo != null && System.currentTimeMillis() - copyFileInfo.getCtime() <= Constants.ONE_MINUTE_MILLISECONDS) {
                //存在最近一分钟内的同名日志文件，就返回；避免频繁拷贝文件
                return ftpLink;
            }
            //需要重新拷贝新的同名日志文件；删除同名日志文件
            FileUtils.deleteQuietly(copyFile);
        }
        try (FileInputStream inputStream = new FileInputStream(srcFile)) {
            long skipChars = srcFile.length() - Config.getSystemConfig().getBrowseBytes();
            if (skipChars > 0) {
                //忽略文件的前一部分，便于拷贝最近的内容
                inputStream.skip(skipChars);
            }
            FileUtils.copyInputStreamToFile(inputStream, copyFile);
        } catch (IOException e) {
            Log.high.error("copyFileToFtpTemp [serviceDir:" + serviceDir + ", fileName:" + fileName + "] error", e);
            return null;
        }
        return ftpLink;
    }

    /**
     * 获取指定目录下的所有文件信息
     * 
     * @param dir 目录路径
     * @return 文件信息列表
     */
    private static List<FileInfo> getFileInfoList(String dir) {
        File dirPath = new File(dir);
        if (!dirPath.exists() || !dirPath.isDirectory()) {
            return null;
        }
        File[] files = dirPath.listFiles();
        if (files == null || files.length == 0) {
            return null;
        }
        List<FileInfo> fileInfoList = new LinkedList<>();
        for (File file : files) {
            if (!file.exists() || file.isDirectory()) {
                continue;
            }
            FileInfo fileInfo = getFileInfo(file.getPath());
            if (fileInfo == null) {
                continue;
            }
            fileInfoList.add(fileInfo);
        }
        return fileInfoList;
    }

    /**
     * 将FileInfo对象转换为LogStruct对象
     * 
     * @param fileInfo 文件信息对象
     * @return 日志结构体对象
     */
    private static LogStruct copyToLogStruct(FileInfo fileInfo) {
        LogStruct logStruct = new LogStruct();
        logStruct.setName(BusinessCheckUtil.getFileName(fileInfo.getName()));
        logStruct.setTime(DateUtil.formatDate(fileInfo.getMtime(), Constants.YYYY_MM_DD_HH_MM_SS));
        logStruct.setSize(DataTransUtil.toBytesStrFormat(fileInfo.getSize()));
        return logStruct;
    }

    /**
     * 获取单个文件的信息
     * 
     * @param filePath 文件路径
     * @return 文件信息对象
     */
    private static FileInfo getFileInfo(String filePath) {
        FileInfo fileInfo;
        try {
            fileInfo = HardInfo.getSigar().getFileInfo(filePath);
        } catch (SigarException e) {
            //内部调用，无需记录到日志
            return null;
        }
        return fileInfo;
    }

    /**
     * 获取文件系统信息列表
     * 包括所有挂载的文件系统的详细信息
     * 
     * @return 文件系统信息列表
     */
    private static List<FileSystemInfoStruct> getFileSystemInfoList() {
        List<FileSystemInfoStruct> list = new ArrayList<>();
        long total = 0L;
        long avail = 0L;
        long used = 0L;
        for (String name : HardInfo.getInstance().getFileSystemInfo()) {
            FileSystemInfoStruct fileSystemInfo = getFileSystemInfo(name);
            if (fileSystemInfo != null) {
                list.add(fileSystemInfo);
                avail += fileSystemInfo.getAvail();
                total += fileSystemInfo.getTotal();
                used += fileSystemInfo.getUsed();
            }
        }
        FileSystemInfoStruct fileSystemInfoStruct = new FileSystemInfoStruct("/", total, avail, used);
        list.add(fileSystemInfoStruct);

        return list;
    }

    /**
     * 获取指定文件系统的详细信息
     * 
     * @param fileSystem 文件系统路径
     * @return 文件系统信息结构体
     */
    private static FileSystemInfoStruct getFileSystemInfo(String fileSystem) {
        if (HardInfo.isWindows()) {
            File file = new File(fileSystem);
            if (file.isFile()) {
                return null;
            }
            long total = file.getTotalSpace();
            long free = file.getFreeSpace();
            long used = total - free;
            return new FileSystemInfoStruct(fileSystem, total, free, used);
        } else {
            try {
                String[] res = ExecUtil.execCmdOnLinux("df | grep " + fileSystem);
                String result = res[0];
                if (!result.isEmpty()) {
                    String line = result.split("\n")[0].trim();
                    if (!line.isEmpty() && line.endsWith(fileSystem)) {
                        String[] lines = line.split(" ");
                        List<String> list = new ArrayList<>();
                        for (String s : lines) {
                            if (!s.isEmpty()) {
                                list.add(s);
                            }
                        }
                        if (list.size() == 6) {
                            long total = Long.parseLong(list.get(1)) * 1024;
                            long used = Long.parseLong(list.get(2)) * 1024;
                            long free = Long.parseLong(list.get(3)) * 1024;
                            return new FileSystemInfoStruct(fileSystem, total, free, used);
                        }
                    }
                }
                return null;
            } catch (Exception e) {
                Log.high.error("getFileSystemInfo error.", e);
                return null;
            }
        }
    }

    /**
     * 获取文件的创建时间
     * 
     * @param path 文件路径
     * @return 文件创建时间戳
     */
    public static long getFileCreateTime(String path) {
        FileInfo fileInfo = getFileInfo(path);
        if (fileInfo == null) {
            return -1L;
        }
        return fileInfo.getCtime();
    }

    /**
     * 执行时间同步
     * 
     * @param time 目标时间戳
     * @return 同步是否成功
     */
    public static boolean execSyncTime(long time) {
        String dateTime = DateUtil.formatDate(time, Constants.YYYY_MM_DD_HH_MM_SS);
        String[] commands;
        if (HardInfo.isLinux()) {
            commands = new String[]{"/bin/bash", "-c", "date -s \'" + dateTime + "\'"};
        } else if (HardInfo.isWindows()) {
            String[] parts = dateTime.split(" ");
            if (parts.length != 2) {
                return false;
            }
            commands = new String[]{"cmd", "/c", "date " + parts[0] + " && time " + parts[1]};
        } else {
            return false;
        }

        try {
            String error = ExecUtil.exec(commands)[1];
            return error.isEmpty();
        } catch (IOException e) {
            Log.high.error("execSyncTime [dateTime:" + dateTime + "] error", e);
            return false;
        }
    }

    /**
     * 获取单主机时间同步信息
     * 
     * @return 时间同步信息结构体
     */
    public static SyncTimeStruct getSyncTimeOneHost() {
        String host = HardInfo.getInstance().getIp();
        String time = DateUtil.formatDate(System.currentTimeMillis(), DateUtil.DATETIME);
        return SyncTimeStruct.valueOf(host, time);
    }

    /**
     * 获取系统详细信息
     * 包括CPU、内存、文件系统等详细信息
     * 
     * @return 系统详细信息结构体
     */
    public static SysInfoDetailStruct getSysInfoDetailOneHost() {
        String host = HardInfo.getInstance().getIp();
        String mac = HardInfo.getInstance().getMac();
        String os = HardInfo.getInstance().getOs();
        List<FsStruct> fsList = getFsInfo();
        FsStruct fs = fsList.remove(fsList.size() - 1);
        CpuStruct cpu = getCpuInfo();
        MemStruct mem = getMemInfo();
        String time = DateUtil.formatDate(System.currentTimeMillis(), DateUtil.DATETIME);
        SysInfoDetailStruct sysInfoDetailStruct = SysInfoDetailStruct.valueOf(host, mac, os, fsList, cpu, mem, time);
        sysInfoDetailStruct.setDiskInfo(fs);
        return sysInfoDetailStruct;
    }

    /**
     * 获取系统概要信息
     * 包括基本的系统状态信息
     * 
     * @return 系统概要信息结构体
     */
    public static SysInfoProfileStruct getSysInfoProfileOneHost() {

        SysInfoDetailStruct struct = getSysInfoDetailOneHost();

        String fs = null;
        String fsTemp = null;
        if (struct.getFs() != null && !struct.getFs().isEmpty()) {
            for (FsStruct fsStruct : struct.getFs()) {
                String name = fsStruct.getName();
                if (name.startsWith("D:") || name.startsWith("/data")) {
                    fs = name + "，占用" + fsStruct.getUsedPercent();
                    break;
                }
                if (name.startsWith("C:") || name.equals("/")) {
                    fsTemp = name + "，占用" + fsStruct.getUsedPercent();
                }
            }
            if (fs == null) {
                fs = fsTemp;
            }
        }

        String mem = null;
        if (struct.getMem() != null) {
            mem = "占用" + struct.getMem().getUsedPercent();
        }

        return SysInfoProfileStruct.valueOf(struct.getHost(), struct.getOs(), fs, mem, struct.getTime());
    }

    /**
     * 获取文件系统信息
     * 
     * @return 文件系统信息列表
     */
    private static List<FsStruct> getFsInfo() {
        List<FsStruct> list = new LinkedList<>();
        List<FileSystemInfoStruct> fileSystemInfoList = getFileSystemInfoList();
        if (fileSystemInfoList.isEmpty()) {
            return list;
        }
        for (FileSystemInfoStruct info : fileSystemInfoList) {
            list.add(FsStruct.valueOf(info));
        }
        return list;
    }

    /**
     * 获取CPU信息
     * 
     * @return CPU信息结构体
     */
    private static CpuStruct getCpuInfo() {
        CpuPerc cpuPerc = getCpuDynamicInfo();
        if (cpuPerc == null) {
            return null;
        }
        CpuStruct cpuStruct = HardInfo.getInstance().getCpuInfo();
        cpuStruct.setUsedPercent(CpuPerc.format(cpuPerc.getUser() + cpuPerc.getSys()));
        return cpuStruct;
    }

    /**
     * 获取CPU动态信息
     * 包括CPU使用率等实时信息
     * 
     * @return CPU动态信息对象
     */
    private static CpuPerc getCpuDynamicInfo() {
        CpuPerc cpuPerc;
        try {
            cpuPerc = HardInfo.getSigar().getCpuPerc();
        } catch (SigarException e) {
            Log.high.error("getCpuDynamicInfo getCpuPerc error", e);
            return null;
        }
        return cpuPerc;
    }

    /**
     * 获取内存信息
     * 
     * @return 内存信息结构体
     */
    private static MemStruct getMemInfo() {
        Mem mem;
        try {
            mem = HardInfo.getSigar().getMem();
        } catch (SigarException e) {
            Log.high.error("getMemInfo getMem error", e);
            return null;
        }
        return MemStruct.valueOf(mem);
    }

    /**
     * 获取操作系统类型
     * 
     * @return 操作系统类型枚举值
     */
    public static int getOsType() {
        if (SigarLoader.IS_LINUX) {
            return OsTypeEnum.LINUX.getType();
        } else if (SigarLoader.IS_WIN32) {
            return OsTypeEnum.WINDOWS.getType();
        }
        return OsTypeEnum.OTHER.getType();
    }
}
