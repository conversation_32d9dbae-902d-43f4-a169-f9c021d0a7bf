package com.maintain.agent.business.service;

import com.common.log.Log;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.struct.SoftwareInfo;
import com.maintain.agent.enums.SoftwareOperateEnum;
import com.maintain.agent.server.SoftwareMonitor;
import com.maintain.agent.utils.FileUtil;
import com.maintain.agent.utils.ProcessUtil;
import com.maintain.agent.utils.ServerRequestUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 监控服务抽象实现类
 * 提供监控数据采集和上报的基础功能，包括：
 * 1. 监控文件读取和解析
 * 2. 监控数据发送到服务器
 * 3. 软件进程状态监控
 * 4. 内存使用监控
 * 
 * 使用线程池异步处理监控数据的上报
 * 
 * <AUTHOR>
 * @date 2017-08-30
 */
public abstract class AbstractMonitoringServiceImpl {

    /**
     * 监控数据上报线程池
     * 核心线程数：1
     * 最大线程数：1
     * 空闲线程存活时间：10分钟
     * 队列容量：100
     * 拒绝策略：调用者运行
     */
    static ThreadPoolExecutor executorService = new ThreadPoolExecutor(1, 1, 10L, TimeUnit.MINUTES, 
        new ArrayBlockingQueue<>(100), new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * 文件操作同步锁
     */
    private static final Object LOCK = new byte[]{};

    /**
     * 时间戳正则表达式
     * 用于匹配文件名中的时间戳部分
     */
    private static final String TIME_REG = "-\\d+\\.json";

    /**
     * 时间戳正则表达式编译后的Pattern对象
     */
    private static final Pattern p = Pattern.compile(TIME_REG);

    /**
     * 读取监控文件内容并发送到服务器
     * 处理流程：
     * 1. 读取指定目录下的所有监控文件
     * 2. 对于软件监控目录，按服务名称和时间戳整理文件
     * 3. 异步发送文件内容到服务器
     * 4. 发送完成后删除已处理的文件
     * 
     * @param dir 监控文件目录
     * @param flag 监控类型标识
     * @throws Exception 处理过程中的异常
     */
    public static void readFileAndSendToServer(final String dir, final int flag) throws Exception {
        File file = new File(dir);
        final HashMap<String, Long> map = new HashMap<>();
        ArrayList<File> list;
        if (file.exists()) {
            Matcher matcher;
            final File[] files = file.listFiles(f -> !f.getName().contains(".tmp"));
            if (files != null && files.length > 0) {
                if (dir.contains("software")) {
                    for (File file1 : files) {
                        String fileName = file1.getName();
                        matcher = p.matcher(fileName);
                        if (matcher.find()) {
                            String json = matcher.group(0);
                            String serviceName = fileName.substring(0, fileName.length() - json.length());
                            Long time = Long.valueOf(json.substring(1, json.length() - 5));
                            Long value = map.get(serviceName);
                            if (value == null || value < time) {
                                map.put(serviceName, time);
                            }
                        }
                    }
                    list = new ArrayList<>(map.size());
                    if (!map.isEmpty()) {
                        final Set<Map.Entry<String, Long>> entries = map.entrySet();
                        for (Map.Entry<String, Long> entry : entries) {
                            list.add(new File(file.getAbsolutePath() + "/" + entry.getKey() + "-" + entry.getValue() + ".json"));
                        }
                    }
                } else {
                    list = new ArrayList<>(files.length);
                    for (File file1 : files) {
                        list.add(file1);
                    }
                }
                executorService.execute(() -> {
                    try {
                        Thread.currentThread().setName("sendFile-Thread");
                        sendToServer(dir, flag, list.toArray(new File[]{}));
                    } catch (IOException | InterruptedException e) {
                        Log.high.error("execute send file to server", e);
                    } finally {
                        for (File file1 : files) {
                            if (file1.exists()) {
                                FileUtil.deleteDir(file1);
                            }
                        }
                    }
                });
            }
        }
    }

    /**
     * 发送监控文件内容到服务器
     * 处理流程：
     * 1. 将文件重命名为临时文件
     * 2. 读取文件内容
     * 3. 解析监控数据
     * 4. 更新软件状态信息
     * 5. 发送数据到服务器
     * 6. 删除临时文件
     * 
     * @param dir 监控文件目录
     * @param flag 监控类型标识
     * @param files 待发送的文件列表
     * @throws IOException IO异常
     * @throws InterruptedException 中断异常
     */
    private static void sendToServer(String dir, int flag, File[] files) throws IOException, InterruptedException {
        File tmpFile;
        for (File file1 : files) {
            if (file1.getName().contains(".tmp")) {
                continue;
            } else {
                if (file1.exists()) {
                    synchronized (LOCK) {
                        if (file1.exists()) {
                            tmpFile = new File(file1.getAbsolutePath() + ".tmp");
                            file1.renameTo(tmpFile);
                            file1 = tmpFile;
                        } else {
                            continue;
                        }
                    }
                } else {
                    continue;
                }
            }
            byte[] bytes = new byte[(int) file1.length()];
            try (FileInputStream fileInputStream = new FileInputStream(file1)) {
                fileInputStream.read(bytes);
            }
            //agent的pid
            String pid = ProcessUtil.getPidByOs();
            if (StringUtils.isEmpty(pid)) {
                pid = FileUtil.readInputStream(new FileInputStream(new File("self.pid")), "UTF-8");
            }
            String content = new String(bytes, Constants.ENCODE_UTF8);
            if (StringUtils.isEmpty(content)) {
                FileUtils.forceDelete(file1);
                continue;
            }
            Map map = Config.getObjectMapper().readValue(content, HashMap.class);
            Object name = map.get("name");
            Object processPidObj = map.get("pid");
            SoftwareInfo softwareInfo = null;
            if (dir.contains("software")) {
                String processPid = "";
                if (name != null) {
                    softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(name.toString());
                    if (processPidObj != null) {
                        processPid = processPidObj.toString();
                        if (softwareInfo != null) {

                            if (softwareInfo.getProgramStatus().equals(SoftwareOperateEnum.CLOSE.getId())) {

                                softwareInfo.setPid(-1);
                            } else {
                                softwareInfo.setPid(Integer.valueOf(processPid));
                                SoftwareMonitor.INSTANCE.updateSoftwareHeartBeatTime(softwareInfo.getName());
                            }
                        }
                    } else if (map.get("info") != null) {
                        Map<String, Object> info = (Map<String, Object>) map.get("info");
                        if (info != null) {
                            if (info.get("common") != null) {
                                Map<String, String> common = (Map<String, String>) info.get("common");
                                processPid = common.get("pid");
                            }
                        }
                    }
                    if (StringUtils.isEmpty(processPid)) {
                        String params = Config.getObjectMapper().writeValueAsString(map);
                        FileUtils.forceDelete(file1);
                        Log.high.error("NoSuchProcessPid：" + params);
                        continue;
                    }
                    long longPid = Long.valueOf(processPid);
                    String startParam = ProcessUtil.getProcArgs(longPid);
                    if (StringUtils.isNotEmpty(startParam)) {
                        // O(n)
                        startParam = StringUtils.deleteWhitespace(startParam);
                    }
                    map.put("startParam", startParam);
                    if (name.toString().contains("agent") || name.toString().contains("Agent")) {
                        processPid = pid;
                    }
                    map.put("processPid", processPid);
                    String processDir = ProcessUtil.getProcessBaseDir() + name;
                    if (StringUtils.isNotEmpty(processDir)) {
                        File processFile = new File(processDir);
                        if (processFile.exists()) {
                            File parentFile = processFile.getParentFile();
                            if (parentFile != null) {
                                map.put("baseDir", parentFile.getAbsolutePath());
                            }
                            map.put("realDir", processFile.getAbsolutePath());
                            if (!map.containsKey("programSize")) {
                                long processSize = FileUtils.sizeOf(processFile);
                                Log.low.info("calc processSize:" + processDir);
                                map.put("programSize", processSize);
                            }
                        }
                    }
                    if (map.get("usedMemory") != null && map.get("totalMemory") != null) {
                        BigDecimal usedMemory = new BigDecimal(map.get("usedMemory").toString());
                        BigDecimal totalMemory = new BigDecimal(map.get("totalMemory").toString());
                        String memoryPercent = usedMemory.multiply(new BigDecimal(100)).divide(totalMemory, 2, BigDecimal.ROUND_HALF_UP).toString();
                        map.put("memory", memoryPercent + "%");
                    }
                }
            }
            map.put("pid", pid);
            //BaseResponse response = null;
            String responseStr = null;
            try {
                String params = Config.getObjectMapper().writeValueAsString(map);

                responseStr = ServerRequestUtil.request(flag, params);

            } catch (Exception e) {
                Log.high.error("agent send monitoring data error" + e.getMessage(), e);
            } finally {
                if (StringUtils.isEmpty(responseStr)) {
                    Log.high.error("agent send monitoring data failed");
                }

                if (file1.exists()) {
                    FileUtils.forceDelete(file1);
                }
            }
        }
    }
}