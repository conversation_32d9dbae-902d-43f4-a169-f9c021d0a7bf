package com.maintain.agent.business.service;

import com.common.log.Log;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.Network;
import com.maintain.agent.entity.struct.CpuStruct;
import com.maintain.agent.entity.struct.NetCardInfoStruct;
import com.maintain.agent.utils.BusinessCheckUtil;
import com.maintain.agent.utils.DataCheckUtil;
import com.maintain.agent.utils.ExecUtil;
import org.apache.commons.lang.StringUtils;
import org.hyperic.sigar.*;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.*;

/**
 * 硬件信息管理类
 * 提供系统硬件信息的采集和管理功能，包括：
 * 1. CPU信息采集
 * 2. 网络信息采集（IP、MAC地址、网卡信息）
 * 3. 文件系统信息采集
 * 4. 操作系统信息采集
 * 
 * 使用Sigar库进行系统信息采集，支持Windows和Linux系统
 * 
 * <AUTHOR>
 * @date 2017/12/21
 */
public class HardInfo {

    /**
     * Sigar实例，用于系统信息采集
     */
    private static Sigar sigar = new Sigar();
    
    /**
     * 操作系统名称，用于区分不同操作系统
     */
    private static String osName = System.getProperty("os.name").toLowerCase();
    
    /**
     * 本机IP地址
     */
    private String ip;
    
    /**
     * 本机MAC地址
     */
    private String mac;
    
    /**
     * 操作系统信息
     */
    private String os;
    
    /**
     * 计算机名称
     */
    private String computerName;
    
    /**
     * CPU信息结构体
     */
    private CpuStruct cpuInfo;
    
    /**
     * 网卡信息列表
     */
    private List<NetCardInfoStruct> netCardInfo;
    
    /**
     * 文件系统信息列表
     */
    private List<String> fileSystemInfo;
    
    /**
     * HardInfo单例实例
     */
    private static HardInfo hardInfo = null;

    /**
     * 网络信息对象
     */
    private Network network;

    /**
     * 内存占用最高的进程列表
     */
    private List<Map<String, Object>> topMemProcessList = new ArrayList<>();
    
    /**
     * CPU占用最高的进程列表
     */
    private List<Map<String, Object>> topCpuProcessList = new ArrayList<>();

    /**
     * Getter method for property <tt>sigar</tt>.
     *
     * @return property value of sigar
     */

    public static Sigar getSigar() {
        return sigar;
    }

    /**
     * Setter method for property <tt>sigar</tt>.
     *
     * @param sigar value to be assigned to property sigar
     */
    public static void setSigar(Sigar sigar) {
        HardInfo.sigar = sigar;
    }

    /**
     * Getter method for property <tt>osName</tt>.
     *
     * @return property value of osName
     */

    public static String getOsName() {
        return osName;
    }

    /**
     * Setter method for property <tt>osName</tt>.
     *
     * @param osName value to be assigned to property osName
     */
    public static void setOsName(String osName) {
        HardInfo.osName = osName;
    }

    /**
     * Getter method for property <tt>ip</tt>.
     *
     * @return property value of ip
     */

    public String getIp() {
        return ip;
    }

    /**
     * Setter method for property <tt>ip</tt>.
     *
     * @param ip value to be assigned to property ip
     */
    public void setIp(String ip) {
        this.ip = ip;
    }

    /**
     * Getter method for property <tt>mac</tt>.
     *
     * @return property value of mac
     */

    public String getMac() {
        return mac;
    }

    /**
     * Setter method for property <tt>mac</tt>.
     *
     * @param mac value to be assigned to property mac
     */
    public void setMac(String mac) {
        this.mac = mac;
    }

    /**
     * Getter method for property <tt>os</tt>.
     *
     * @return property value of os
     */

    public String getOs() {
        return os;
    }

    /**
     * Setter method for property <tt>os</tt>.
     *
     * @param os value to be assigned to property os
     */
    public void setOs(String os) {
        this.os = os;
    }

    /**
     * Getter method for property <tt>computerName</tt>.
     *
     * @return property value of computerName
     */

    public String getComputerName() {
        return computerName;
    }

    /**
     * Setter method for property <tt>computerName</tt>.
     *
     * @param computerName value to be assigned to property computerName
     */
    public void setComputerName(String computerName) {
        this.computerName = computerName;
    }

    /**
     * Getter method for property <tt>cpuInfo</tt>.
     *
     * @return property value of cpuInfo
     */

    public CpuStruct getCpuInfo() {
        return cpuInfo;
    }

    /**
     * Setter method for property <tt>cpuInfo</tt>.
     *
     * @param cpuInfo value to be assigned to property cpuInfo
     */
    public void setCpuInfo(CpuStruct cpuInfo) {
        this.cpuInfo = cpuInfo;
    }

    /**
     * Getter method for property <tt>netCardInfo</tt>.
     *
     * @return property value of netCardInfo
     */

    public List<NetCardInfoStruct> getNetCardInfo() {
        return netCardInfo;
    }

    /**
     * Setter method for property <tt>netCardInfo</tt>.
     *
     * @param netCardInfo value to be assigned to property netCardInfo
     */
    public void setNetCardInfo(List<NetCardInfoStruct> netCardInfo) {
        this.netCardInfo = netCardInfo;
    }

    /**
     * Getter method for property <tt>fileSystemInfo</tt>.
     *
     * @return property value of fileSystemInfo
     */

    public List<String> getFileSystemInfo() {
        return fileSystemInfo;
    }

    /**
     * Setter method for property <tt>fileSystemInfo</tt>.
     *
     * @param fileSystemInfo value to be assigned to property fileSystemInfo
     */
    public void setFileSystemInfo(List<String> fileSystemInfo) {
        this.fileSystemInfo = fileSystemInfo;
    }


    /**
     * 得到HardInfo实例（单例模式）
     *
     * @return 返回此类的实例
     */
    public static HardInfo getInstance() {
        if (hardInfo == null) {
            StringBuilder mac = new StringBuilder();
            hardInfo = new HardInfo();
            hardInfo.ip = DataCheckUtil.getIP(Config.getSystemConfig().getMaintainAgent());
            hardInfo.computerName = getCompName();
            hardInfo.cpuInfo = getCpuInfoStruct();
            hardInfo.netCardInfo = getNetCardInfoList();
            hardInfo.fileSystemInfo = getDiskNameList();
            hardInfo.mac = mac.toString();
            if (isWindows()) {
                try {
                    String[] res = ExecUtil.execCmdOnWindows("wmic os get Caption");
                    hardInfo.os = res[0].split("\r\n\r\n")[1].trim();
                } catch (Exception e) {
                    Log.high.error("execute wmic error", e);
                    try {
                        String[] result = ExecUtil.execCmdOnWindows("ver");
                        hardInfo.os = result[0];
                    } catch (Exception e1) {
                        hardInfo.os = System.getProperty("os.name");
                    }
                }
            } else {
                String res = null;
                try {
                    //centos 7.2以及6.5能够正确返回操作系统及其版本号
                    res = ExecUtil.execCmdOnLinux("cat /etc/redhat-release")[0];
                } catch (Exception e) {
                    Log.high.error("get os version error.", e);
                }
                if (res == null || res.isEmpty()) {
                    hardInfo.os = "Linux";
                } else {
                    res = res.split("\n")[0].trim();
                    res = res.split("\\(")[0].replace("release ", "").replace("Linux ", "").trim();
                    hardInfo.os = res;
                }
                if ("/S".equals(hardInfo.os)) {
                    hardInfo.os = System.getProperty("os.name");
                }
            }
        }
        return hardInfo;
    }


    /**
     * 获取网卡信息列表
     * 通过Sigar库获取系统中所有网卡的详细信息
     * 
     * @return 网卡信息列表
     */
    private static List<NetCardInfoStruct> getNetCardInfoList() {
        List<NetCardInfoStruct> list = BusinessCheckUtil.getNetCardInfoList();
        if (list == null) {
            return new ArrayList<>();
        }
        for (NetCardInfoStruct struct : list) {
            String name = struct.getNetInterfaceConfig().getName();
            String speed = null;
            if (isWindows()) {
                try {
                    NetInterfaceStat stat = sigar.getNetInterfaceStat(name);
                    if (stat != null) {
                        speed = stat.getSpeed() + "";
                    }
                } catch (SigarException e) {
                    continue;
                }
            } else {
                speed = getLinuxNicSpeed(name);
                struct.setSpeed(speed);
                continue;
            }
            try {
                Long sp = StringUtils.isEmpty(speed) ? 0 : Long.valueOf(speed);
                speed = (sp / 10000) + "Mb/s";
            } catch (Exception e) {
                Log.low.debug(e.getMessage(), e);
            }
            struct.setSpeed(speed);
        }
        return list;
    }

    /**
     * 根据IP地址获取网卡信息
     * 
     * @param ip IP地址
     * @param mac MAC地址输出参数
     * @return 网卡信息列表
     */
    private static List<NetCardInfoStruct> getNetCardInfoList(String ip, StringBuilder mac) {

        try {
            String[] netNames = sigar.getNetInterfaceList();
            List<NetCardInfoStruct> list = new ArrayList<>();
            for (int i = 0; i < netNames.length; i++) {
                NetCardInfoStruct struct = new NetCardInfoStruct();
                // struct.setNetInterfaceConfig(sigar.getNetInterfaceConfig(netNames[i]));
                if (isWindows()) {
                    struct.setSpeed(sigar.getNetInterfaceStat(netNames[i]).getSpeed() + "");
                } else {
                    struct.setSpeed(getLinuxNicSpeed(netNames[i]));
                }
                list.add(struct);
            }

            try {
                InetAddress address = getAddressByIp(ip);
                mac.append(getMacByAddress(address));
            } catch (Exception e) {
                Log.high.error("get mac error.", e);
            }
            if (mac.toString().isEmpty()) {
                mac.append(list.get(0).getNetInterfaceConfig().getHwaddr());
            }
            /*for (NetCardInfoStruct struct: list) {
                NetInterfaceConfig config = struct.getNetInterfaceConfig();
                System.out.println(config.getName() + ".." + config.getHwaddr());
            }*/

            // 过滤,并将多个name合并为1个
            List<NetCardInfoStruct> filterList = new ArrayList<>();
            Set<String> filter = new HashSet<>();
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) == null) {
                    continue;
                }
                NetInterfaceConfig netConfig = list.get(i).getNetInterfaceConfig();
                if (netConfig == null || StringUtils.isEmpty(netConfig.getHwaddr())) {
                    continue;
                }
                if (filter.add(netConfig.getHwaddr())) {
                    filterList.add(list.get(i));
                }
            }
           /* for (NetCardInfoStruct struct :filterList) {
                System.out.println(struct.getNetInterfaceConfig().getHwaddr());
            }*/
            return filterList;
        } catch (Exception e) {
            Log.high.error("get netCard info error.", e);
            return new ArrayList<>();
        }
    }

    /**
     * 根据网络地址获取MAC地址
     * 
     * @param address 网络地址
     * @return MAC地址字符串
     * @throws SocketException 网络异常
     */
    public static String getMacByAddress(InetAddress address) throws SocketException {
        NetworkInterface ni = NetworkInterface.getByInetAddress(address);
        if (ni == null) {
            return "";
        }
        byte[] mac = ni.getHardwareAddress();
        StringBuilder macRes = new StringBuilder();
        for (int i = 0; i < mac.length; i++) {
            macRes.append(String.format("%02X%s", mac[i], (i < mac.length - 1) ? "-" : ""));
        }
        return macRes.toString();
    }

    /**
     * 根据IP地址获取网络地址对象
     * 
     * @param ip IP地址
     * @return 网络地址对象
     * @throws UnknownHostException 未知主机异常
     */
    public static InetAddress getAddressByIp(String ip) throws UnknownHostException {
        String[] ips = ip.split("\\.");
        byte[] buf = new byte[4];
        for (int i = 0; i < 4; i++) {
            buf[i] = (byte) (Integer.parseInt(ips[i]) & 0xff);
        }
        return InetAddress.getByAddress(buf);
    }


    /**
     * 获取CPU信息结构体
     * 通过Sigar库获取CPU的详细信息
     * 
     * @return CPU信息结构体
     */
    private static CpuStruct getCpuInfoStruct() {
        CpuInfo[] cpuInfos;
        try {
            cpuInfos = sigar.getCpuInfoList();
            CpuStruct cpuStruct = new CpuStruct();
            if (cpuInfos != null && cpuInfos.length > 0) {
                cpuStruct.setType(cpuInfos[0].getModel());
                cpuStruct.setTotalCore(cpuInfos[0].getTotalCores() + "");
                cpuStruct.setVendor(cpuInfos[0].getVendor());
                cpuStruct.setMhz(cpuInfos[0].getMhz() + "MHz");
            }
            return cpuStruct;
        } catch (Exception e) {
            Log.high.error("get cpu info error.", e);
            return null;
        }
    }

    /**
     * 获取计算机名称
     * 
     * @return 计算机名称
     */
    private static String getCompName() {
        try {
            if (isWindows()) {
                return ExecUtil.runCmdCommend("cmd /c hostname")[0].trim();
            } else {
                return ExecUtil.execCmdOnLinux("hostname")[0].trim();
            }
        } catch (Exception e) {
            Log.high.error("get computer name error.", e);
            return "";
        }
    }

    /**
     * 获取磁盘名称列表
     * 
     * @return 磁盘名称列表
     */
    private static List<String> getDiskNameList() {
        FileSystem[] fileSystemList;
        try {
            fileSystemList = sigar.getFileSystemList();
        } catch (SigarException e) {
            Log.high.error("getDiskNameList error", e);
            return new ArrayList<>();
        }

        List<String> list = new ArrayList<>();
        for (FileSystem fileSystem : fileSystemList) {
            if ("local".equalsIgnoreCase(fileSystem.getTypeName())) {
                list.add(fileSystem.getDirName());
            }
        }
        return list;
    }

    /**
     * 获取Linux系统网卡速率
     * 
     * @param nicName 网卡名称
     * @return 网卡速率
     */
    private static String getLinuxNicSpeed(String nicName) {
        String cmd = "ethtool " + nicName + " | grep Speed";
        try {
            String res = ExecUtil.execCmdOnLinux(cmd)[0];
            return res.replace("Speed:", "").trim();
        } catch (Exception e) {
            Log.high.error("getLinuxNicSpeed error, nicName=" + nicName, e);
            return null;
        }
    }

    /**
     * 判断是否为Windows系统
     * 
     * @return 如果是Windows系统返回true，否则返回false
     */
    public static boolean isWindows() {
        return osName.contains(Constants.OS_WINDOWS);
    }

    /**
     * 判断是否为Linux系统
     * 
     * @return 如果是Linux系统返回true，否则返回false
     */
    public static boolean isLinux() {
        return osName.contains(Constants.OS_LINUX);
    }


    /**
     * 获取网络信息
     * 
     * @return 网络信息对象
     */
    public Network getNetInfo() {
        if (network == null) {
            network = new Network();
            try {
                String[] ifNames = sigar.getNetInterfaceList();
                for (String name : ifNames) {
                    NetInterfaceConfig ifConfig = sigar.getNetInterfaceConfig(name);
                    if (!ifConfig.getAddress().equals(ip)) {
                        continue;
                    }
                    network.setName(name);
                    break;
                }
            } catch (Exception e) {
                Log.high.error("get netinfo error", e);
                network = null;
                return null;
            }
        }
        if (StringUtils.isEmpty(network.getName())) {
            network = null;
            Log.high.error("当前机器没有IP是：" + ip + "的网卡");
            return null;
        }
        try {
            NetInterfaceStat ifStat = sigar.getNetInterfaceStat(network.getName());
            long receiveBytes = ifStat.getRxBytes();
            long sendBytes = ifStat.getTxBytes();
            Network result = new Network();
            if (network.getTimestamp() != 0) {
                result.setReceiveBytes(receiveBytes - network.getReceiveBytes());
                result.setSendBytes(sendBytes - network.getSendBytes());
                //重启网卡，导致网卡的字节数清零
                if (result.getReceiveBytes() < 0 || result.getSendBytes() < 0) {
                    initNetwork(sendBytes, receiveBytes);
                    return null;
                }
                result.setTimestamp(System.currentTimeMillis() / 1000);
                result.setInterval(result.getTimestamp() - network.getTimestamp());
                network.setSendBytes(sendBytes);
                network.setReceiveBytes(receiveBytes);
                return result;
            } else {
                initNetwork(sendBytes, receiveBytes);
            }
        } catch (Exception e) {
            Log.high.error("get netinfo error", e);
        }
        return null;
    }

    /**
     * 初始化网络信息
     * 
     * @param sendBytes 发送字节数
     * @param receiveBytes 接收字节数
     */
    private void initNetwork(long sendBytes, long receiveBytes) {
        network.setSendBytes(sendBytes);
        network.setReceiveBytes(receiveBytes);
        network.setTimestamp(System.currentTimeMillis() / 1000);
    }
}
