package com.maintain.agent.business.service;

import com.common.log.Log;
import com.maintain.agent.business.helper.BusinessHelper;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.struct.FsStruct;
import com.maintain.agent.entity.struct.SysInfoDetailStruct;
import com.maintain.agent.utils.DataTransUtil;
import com.maintain.agent.utils.DateUtil;
import com.maintain.agent.utils.ListUtil;
import com.maintain.agent.utils.ProcessUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.hyperic.sigar.ProcCpu;
import org.hyperic.sigar.ProcMem;

import java.io.*;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2017-08-30
 */
public class MonitoringServiceImpl extends AbstractMonitoringServiceImpl {
    /**
     * 硬件信息监控目录
     */
    private static String hardwareMonitoringDir;
    /**
     * 软件信息监控目录
     */
    private static String softwareMonitoringDir;

    /**
     * Getter method for property <tt>hardwareMonitoringDir</tt>.
     *
     * @return property value of hardwareMonitoringDir
     */

    public static String getHardwareMonitoringDir() {
        return hardwareMonitoringDir;
    }

    /**
     * Setter method for property <tt>hardwareMonitoringDir</tt>.
     *
     * @param hardwareMonitoringDir value to be assigned to property hardwareMonitoringDir
     */
    public static void setHardwareMonitoringDir(String hardwareMonitoringDir) {
        MonitoringServiceImpl.hardwareMonitoringDir = hardwareMonitoringDir;
    }

    /**
     * Getter method for property <tt>softwareMonitoringDir</tt>.
     *
     * @return property value of softwareMonitoringDir
     */

    public static String getSoftwareMonitoringDir() {
        return softwareMonitoringDir;
    }

    /**
     * Setter method for property <tt>softwareMonitoringDir</tt>.
     *
     * @param softwareMonitoringDir value to be assigned to property softwareMonitoringDir
     */
    public static void setSoftwareMonitoringDir(String softwareMonitoringDir) {
        MonitoringServiceImpl.softwareMonitoringDir = softwareMonitoringDir;
    }

    static {
        if (HardInfo.isWindows()) {
            hardwareMonitoringDir = Config.getSystemConfig().getHardwareMonitoringDirWindow();
            softwareMonitoringDir = Config.getSystemConfig().getSoftwareMonitoringDirWindow();
        } else if (HardInfo.isLinux()) {
            hardwareMonitoringDir = Config.getSystemConfig().getHardwareMonitoringDirLinux();
            softwareMonitoringDir = Config.getSystemConfig().getSoftwareMonitoringDirLinux();
        } else {
            hardwareMonitoringDir = "";
            softwareMonitoringDir = "";
            Log.high.error("init monitoringDir error,os=" + HardInfo.getOsName());
        }
    }

    /**
     * 监控总流程方法体
     *
     * @throws Exception 抛出异常
     */
    public static void mainOfHardwareMonitoring() throws Exception {
        Thread.currentThread().setName("hardwareMonitoringThread");
        //TODO 都已经准备好了，为什么还要先写文件再读然后传？
        Map dataMap = getMonitoringData();
        writeDataToFile(dataMap);
        readFileAndSendToServer(hardwareMonitoringDir, Constants.AGENT_SEND_HARDWARE_MONITORING_DATA);
    }


    /**
     * 将数据写出到文件
     *
     * @param dataMap 数据
     * @throws Exception 抛出异常
     */
    private static void writeDataToFile(Map dataMap) throws Exception {
        File file = new File(hardwareMonitoringDir);
        if (!file.exists()) {
            FileUtils.forceMkdir(file);
        }
        String fileName = hardwareMonitoringDir + File.separator + System.currentTimeMillis() + ".json";
        try (FileOutputStream fos = new FileOutputStream(fileName, false)) {
            fos.write(Config.getObjectMapper().writeValueAsString(dataMap).getBytes(Constants.ENCODE_UTF8));
            fos.flush();
        }
    }

    /**
     * 主机硬件监控数据获取
     *
     * @throws Exception 抛出异常
     */
    public static Map getMonitoringData() throws Exception {
        Map<String, Object> result = new HashMap<>();
        result.put("ip", HardInfo.getInstance().getIp());
        result.put("ipArray", getIps());
        result.put("type", "hardware");
        result.put("createTime", DateUtil.formatDate(new Date(), Constants.YYYY_MM_DD_HH_MM_SS));
        final Set<Integer> ports = ProcessUtil.getPorts();
        result.put("ports", ports.size());
        Map<String, Object> infoMap = new HashMap<>();
        SysInfoDetailStruct sysInfoDetailOneHost = BusinessHelper.getSysInfoDetailOneHost();
        infoMap.put("name", HardInfo.getInstance().getComputerName());
        infoMap.put("osName", HardInfo.getInstance().getOs());
        infoMap.put("ports", ports);
        infoMap.put("cpu", sysInfoDetailOneHost.getCpu());
        infoMap.put("mem", sysInfoDetailOneHost.getMem());
        infoMap.put("disk", sysInfoDetailOneHost.getFs());
        infoMap.put("diskInfo", sysInfoDetailOneHost.getDiskInfo());
        infoMap.put("nic", HardInfo.getInstance().getNetCardInfo());
        infoMap.put("network", HardInfo.getInstance().getNetInfo());

        String osName = System.getProperty("os.name").toLowerCase();

        Integer firewallStatus = null;
        if (osName.contains(Constants.OS_WINDOWS)) {
            String command = "cmd /c REG QUERY HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\SharedAccess\\Parameters\\FirewallPolicy\\StandardProfile |findstr \"EnableFirewall \"";
            Process process = Runtime.getRuntime().exec(command);
            String cmdResult = read(process, Constants.ENCODE_GBK);
            if (StringUtils.isNotEmpty(cmdResult)) {
                if (cmdResult.contains("0x0")) {
                    firewallStatus = 0;
                } else if (cmdResult.contains("0x1")) {
                    firewallStatus = 1;
                }
            }
            process.destroy();
        } else if (osName.contains(Constants.OS_LINUX)) {
            String command = null;
            String cmdResult = null;
            Process process = null;
            try {
                command = "cat /etc/redhat-release";
                process = Runtime.getRuntime().exec(command);
                cmdResult = read(process, Constants.ENCODE_UTF8);
                if (cmdResult.contains("6.5")) {
                    command = "service iptables status";
                    process = Runtime.getRuntime().exec(command);
                    cmdResult = read(process, Constants.ENCODE_UTF8);
                    if (cmdResult.contains("not") || cmdResult.contains("未") || cmdResult.contains("dead")) {
                        firewallStatus = 0;
                    } else if (cmdResult.contains("fail")) {
                        firewallStatus = 0;
                    } else {
                        firewallStatus = 1;
                    }
                } else {
                    command = "firewall-cmd --state";
                    process = Runtime.getRuntime().exec(command);
                    cmdResult = read(process, Constants.ENCODE_UTF8);
                    if (cmdResult.contains("not running") || (cmdResult.contains("not") && cmdResult.contains("running"))) {
                        firewallStatus = 0;
                    } else {
                        firewallStatus = 1;
                    }
                }
            } catch (Exception e) {
                command = "iptables -L";
                process = Runtime.getRuntime().exec(command);
                cmdResult = read(process, Constants.ENCODE_UTF8);
                if (cmdResult.contains("tcp") || cmdResult.contains("anywhere")) {
                    firewallStatus = 1;
                } else {
                    firewallStatus = 0;
                }
            }
            try {

                String cpuTopResult = ProcessUtil.execute("ps -eo pid,comm,%mem,%cpu --sort=-%cpu|head -4", false);
                List<String> cpuTopPidList = new ArrayList<>(3);
                List<Map<String, Object>> cpuTopProcessInfo = new ArrayList<>(3);
                String[] lines = cpuTopResult.split("\n");

                for (int i = 1; i < lines.length; i++) {
                    String[] infos = lines[i].split("\\s+");
                    String pid = infos[1];
                    cpuTopPidList.add(pid);
                    Map<String, Object> processInfo = queryProcessInfo(pid);
                    processInfo.put("name", infos[2]);
                    cpuTopProcessInfo.add(processInfo);
                }
                String memTopResult = ProcessUtil.execute("ps -eo pid,comm,%mem,%cpu --sort=-%mem|head -4", false);
                List<Map<String, Object>> memTopProcessInfo = new ArrayList<>();
                lines = memTopResult.split("\n");
                for (int i = 1; i < lines.length; i++) {
                    String[] infos = lines[i].split("\\s+");
                    String pid = infos[1];
                    if (cpuTopPidList.contains(pid)) {
                        memTopProcessInfo.add(cpuTopProcessInfo.get(cpuTopPidList.indexOf(pid)));
                    } else {
                        Map<String, Object> processInfo = queryProcessInfo(pid);
                        processInfo.put("name", infos[2]);
                        memTopProcessInfo.add(processInfo);
                    }
                }

                result.put("cpuTopProcessInfo", cpuTopProcessInfo);
                result.put("memTopProcessInfo", memTopProcessInfo);
            } catch (Exception e) {
                Log.high.error("收集TOP进程信息出错", e);
            }

            List<FsStruct> fsStructList = sysInfoDetailOneHost.getFs();
            if (ListUtil.isNotEmpty(fsStructList)) {
                FsStruct newFsStruct = new FsStruct();
                Long total = 0L;
                Long used = 0L;
                for (FsStruct fsStruct : fsStructList) {
                    total += DataTransUtil.toBytesLongFormat(fsStruct.getTotal());
                    used += DataTransUtil.toBytesLongFormat(fsStruct.getUsed());
                }
                if (total > 0 && used > 0) {
                    newFsStruct.setUsed(DataTransUtil.toBytesStrFormat(used));
                    newFsStruct.setTotal(DataTransUtil.toBytesStrFormat(total));
                    newFsStruct.setAvail(DataTransUtil.toBytesStrFormat((total - used)));
                    newFsStruct.setUsedPercent(DataTransUtil.toPercentStrFormat(used * 100.0 / total));
                    sysInfoDetailOneHost.setDiskInfo(newFsStruct);
                    infoMap.put("diskInfo", newFsStruct);
                }
            }
            process.destroy();
        }
        infoMap.put("firewallStatus", firewallStatus);
        result.put("info", infoMap);
        return result;
    }

    private static Map<String, Object> queryProcessInfo(String pid) {
        try {
            ProcCpu procCpu = HardInfo.getSigar().getProcCpu(pid);
            ProcMem procMem = HardInfo.getSigar().getProcMem(pid);

            double cpuPercent = procCpu.getPercent();
            //虚拟总内存，单位：B
            long memSize = procMem.getSize();
            //常驻使用内存，单位：B
            long memRssSize = procMem.getResident();

            Map<String, Object> record = new HashMap<>(16);
            int availableProcessors = Runtime.getRuntime().availableProcessors();
            record.put("pid", Integer.parseInt(pid));
            record.put("cpuPercent", cpuPercent / availableProcessors * 100);
            record.put("memSize", memSize);
            record.put("memRssSize", memRssSize);
            record.put("capTime", DateUtil.formatDate(new Date(), DateUtil.DATE_TIME_FORMATTER));

            //程序命令行
            record.put("command", String.join(" ", HardInfo.getSigar().getProcArgs(pid)));
            return record;
        } catch (Exception e) {
            Log.high.error("收集进程信息出错", e);
            return new HashMap<>();
        }
    }

    private static List<String> getIps() {
        List<String> ips = new ArrayList<>();
        try {
            Enumeration<NetworkInterface> e1 = NetworkInterface.getNetworkInterfaces();
            while (e1.hasMoreElements()) {
                NetworkInterface ni = e1.nextElement();
                Enumeration<InetAddress> e2 = ni.getInetAddresses();
                while (e2.hasMoreElements()) {
                    InetAddress ip = e2.nextElement();
                    if (ip != null && ip instanceof Inet4Address) {
                        if (!ip.getHostAddress().equals(Constants.LOCAL_IP)) {
                            ips.add(ip.getHostAddress());
                        }
                    }
                }
            }
        } catch (SocketException e) {
            Log.high.error("getLocalIp error," + e.getMessage());
        }
        return ips;
    }

    private static String read(Process process, String charSet) throws IOException {
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream(), charSet));
        String line = null;
        StringBuilder sb = new StringBuilder();
        while ((line = reader.readLine()) != null) {
            sb.append(line);
            sb.append("\n");
        }
        return sb.toString().trim();
    }
}