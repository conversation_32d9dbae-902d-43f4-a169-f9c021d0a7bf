package com.maintain.agent.software;

import com.common.log.Log;
import com.common.util.SigarUtil;
import com.maintain.agent.entity.struct.SoftwareInfo;
import com.maintain.agent.server.SoftwareMonitor;
import com.maintain.agent.utils.ExecUtil;
import com.maintain.agent.utils.ProcessUtil;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


public class BaseSoftware{

    private static final Pattern WIN_PID_REG = Pattern.compile("[0-9]+(?=[^0-9]*$)");

    private static final Pattern LINUX_PID_REG = Pattern.compile("\\d+");

    private String name;

    private String jpsKey;

    private String[] softwareKeys;

    private String startScriptPath;

    private String stopScriptPath;

    private String startScript;

    private String stopScript;

    public BaseSoftware() {
    }

    public BaseSoftware(String name, String[] softwareKeys, String startScriptPath, String startScript, String stopScriptPath, String stopScript) {
        this.name = name;
        this.softwareKeys = softwareKeys;
        this.startScriptPath = startScriptPath;
        this.startScript = startScript;
        this.stopScriptPath = stopScriptPath;
        this.stopScript = stopScript;
    }

    public void openSoftware(String softwarePath) {
        final SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(name);
        if(softwareInfo == null){
            throw new RuntimeException(name + "程序信息不存在");
        }
        final Integer processCount = softwareInfo.getProcessCount();
        final int size = getPidSet().size();
        if(size < processCount && size != 0){
            closeSoftware(softwareInfo.getRealDir());
        }else if(size == processCount){
            return;
        }
        final String startScriptPath = getStartScriptPath();
        final String startScript = getStartScript();
        if (startScript == null || "".equals(startScript)) {
            throw new RuntimeException("启动 " + getName() + " 失败，启动脚本路径不存在");
        }
        final StringBuilder stringBuilder = new StringBuilder();
        if(ProcessUtil.isWindows()){
            stringBuilder.append("cd /d \"").append(softwarePath).append(File.separator);
            if(startScriptPath != null){
                stringBuilder.append(startScriptPath);
            }
            stringBuilder.append("\" && ").append(startScript);
        }else{
            stringBuilder.append("cd ").append(softwarePath).append(File.separator);
            if(startScriptPath != null){
                stringBuilder.append(startScriptPath);
            }
            stringBuilder.append(" && ").append(startScript);
        }
        ProcessUtil.execLocalCommand(stringBuilder.toString());
    }

    public void closeSoftware(String softwarePath) {
        Log.low.info("#{softwarePath}: " + softwarePath);
        final String stopScriptPath = getStopScriptPath();
        final String stopScript = getStopScript();
        if (stopScript != null && !"".equals(stopScript)) {
            final StringBuilder stringBuilder = new StringBuilder();
            if(ProcessUtil.isWindows()){
                stringBuilder.append("cd /d ").append(softwarePath).append(File.separator);
                if(stopScriptPath != null){
                    stringBuilder.append(stopScriptPath);
                }
                stringBuilder.append(" && ").append(stopScript);
            }else{
                stringBuilder.append("cd ").append(softwarePath).append(File.separator);
                if(stopScriptPath != null){
                    stringBuilder.append(stopScriptPath);
                }
                stringBuilder.append(" && ").append(stopScript);
            }
            ProcessUtil.execLocalCommand(stringBuilder.toString());
        } else {
            final Set<Integer> pidSet = getPidSet();
            if (!pidSet.isEmpty()) {
                String killCommand;
                if (ProcessUtil.isWindows()) {
                    killCommand = "taskkill /f /pid ";
                } else {
                    killCommand = "kill -9 ";
                }
                for (Integer pid : pidSet) {
                    try {
                        SigarUtil.sigar.kill(pid, 9);
                    } catch (Exception e) {
                        Log.high.debug(e.getMessage(), e);
                    }
                    ProcessUtil.execLocalCommand(killCommand + pid);
                }
            }
        }
    }

    public Set<Integer> getPidSet() {
        final LinkedHashSet<Integer> pidSet = new LinkedHashSet<>();
        if (ProcessUtil.isWindows()) {
            try {
                String[] strings = ExecUtil.execCmdOnWindows("wmic process where  \"caption like 'java%.exe'\" get commandline,processid");
                if (strings != null && strings.length > 0) {
                    boolean boo;
                    final String[] softwareKeys = getSoftwareKeys();
                    if (softwareKeys == null || softwareKeys.length < 1) {
                        throw new RuntimeException(getName() + " softwarekeys 不存在");
                    }
                    List<String> softwareStrings = new LinkedList<>();

                    for (String string : strings) {
                        final String[] split = string.split("\n");
                        out:
                        for (String s : split) {
                            for (int i = 0; i < softwareKeys.length; i++) {
                                if (s.contains(softwareKeys[i])) {
                                    boo = true;
                                } else {
                                    boo = false;
                                    continue out;
                                }
                                if (i == softwareKeys.length - 1 && boo) {
                                    softwareStrings.add(s);
                                }
                            }
                        }

                    }
                    if (!softwareStrings.isEmpty()) {
                        for (String softwareString : softwareStrings) {
                            final Matcher matcher = WIN_PID_REG.matcher(softwareString);
                            if (matcher.find()) {
                                pidSet.add(Integer.valueOf(matcher.group(0)));
                            }
                        }
                    }
                }
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
        } else {
            final String jpsKey = getJpsKey();
            if (jpsKey != null && !"".equals(jpsKey.trim())) {
                try {
                    String[] result = ExecUtil.execCmdOnLinux("jps");
                    Log.low.info("执行jps的结果: \n" + result);
                    if (result != null && result.length > 0) {
                        for (String jp : result) {
                            final String[] split = jp.split("\n");
                            for (String js : split) {
                                if (js != null) {
                                    if (js.contains(jpsKey)) {
                                        final Matcher matcher = LINUX_PID_REG.matcher(js);
                                        if (matcher.find()) {
                                            pidSet.add(Integer.valueOf(matcher.group(0)));
                                        }
                                    }
                                }
                            }
                        }
                    }
                } catch (IOException e) {
                    Log.high.error("执行jps获取进程号出错", e);
                }
            }
            final String[] softwareKeys = getSoftwareKeys();
            if (softwareKeys == null || softwareKeys.length < 1) {
                throw new RuntimeException(getName() + " softwarekeys 不存在");
            }
            final StringJoiner stringJoiner = new StringJoiner(" |grep ", "ps -ef | grep -v grep | grep ", "");
            for (String softwareKey : softwareKeys) {
                stringJoiner.add(softwareKey);
            }
            String command = stringJoiner.toString();
            try {
                String[] result = ExecUtil.execCmdOnLinux(command);

                if (result != null && result.length > 0) {
                    for (String jp : result) {
                        Log.low.info("执行"  + command +  "的结果: \n" + jp);
                        Matcher matcher = LINUX_PID_REG.matcher(jp);
                        if (matcher.find()) {
                            String pid = matcher.group(0);
                            pidSet.add(Integer.parseInt(pid.trim()));
                        }
                    }
                }
            } catch (IOException e) {
                Log.high.error("执行ps命令获取进程号出错", e);
            }
        }
        Log.low.info("#{softwareManageService != null时的pidSet:}" + pidSet);
        return pidSet;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getJpsKey() {
        return jpsKey;
    }

    public void setJpsKey(String jpsKey) {
        this.jpsKey = jpsKey;
    }

    public String[] getSoftwareKeys() {
        return softwareKeys;
    }

    public void setSoftwareKeys(String[] softwareKeys) {
        this.softwareKeys = softwareKeys;
    }

    public String getStartScriptPath() {
        return startScriptPath;
    }

    public void setStartScriptPath(String startScriptPath) {
        this.startScriptPath = startScriptPath;
    }

    public String getStopScriptPath() {
        return stopScriptPath;
    }

    public void setStopScriptPath(String stopScriptPath) {
        this.stopScriptPath = stopScriptPath;
    }

    public String getStartScript() {
        return startScript;
    }

    public void setStartScript(String startScript) {
        this.startScript = startScript;
    }

    public String getStopScript() {
        return stopScript;
    }

    public void setStopScript(String stopScript) {
        this.stopScript = stopScript;
    }

    @Override
    public String toString() {
        return "Software{" +
                "name='" + name + '\'' +
                ", jpsKey='" + jpsKey + '\'' +
                ", softwareKeys=" + Arrays.toString(softwareKeys) +
                ", startScriptPath='" + startScriptPath + '\'' +
                ", stopScriptPath='" + stopScriptPath + '\'' +
                ", startScript='" + startScript + '\'' +
                ", stopScript='" + stopScript + '\'' +
                '}';
    }
}
