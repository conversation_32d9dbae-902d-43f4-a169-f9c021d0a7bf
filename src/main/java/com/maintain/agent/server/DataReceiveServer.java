package com.maintain.agent.server;

import com.alibaba.fastjson.JSON;
import com.common.log.Log;
import com.common.util.ObjectMapperUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.maintain.agent.business.helper.ReqRespHelper;
import com.maintain.agent.business.manager.MaintainAgentManager;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.DeleteResultVo;
import com.maintain.agent.entity.MysqlBackupVo;
import com.maintain.agent.entity.request.*;
import com.maintain.agent.entity.response.BaseResponse;
import com.maintain.agent.entity.struct.AnsecProcesskeepVo;
import com.maintain.agent.entity.struct.OperateTypeStruct;
import com.maintain.agent.entity.struct.SoftwareInfo;
import com.maintain.agent.enums.OperateTypeEnum;
import com.maintain.agent.enums.SoftwareOperateEnum;
import com.maintain.agent.utils.*;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.filefilter.FileFilterUtils;
import org.apache.commons.io.monitor.FileAlterationListener;
import org.apache.commons.io.monitor.FileAlterationListenerAdaptor;
import org.apache.commons.io.monitor.FileAlterationMonitor;
import org.apache.commons.io.monitor.FileAlterationObserver;
import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.yaml.snakeyaml.Yaml;

import java.io.*;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.lang.reflect.InvocationTargetException;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2016/10/31
 * ICE 接口服务类
 */
@RestController
@RequestMapping("/agent")
public class DataReceiveServer {
    private String localIp;

    static {
        String path = "./conf/script";
        fileMonitor(path);
    }

    private static void fileMonitor(String path) {
        try {
            File file = new File(path);
            if (!file.exists()) {
                return;
            }
            String filePath = file.getParent();
            FileAlterationMonitor monitor = new FileAlterationMonitor();
            FileAlterationObserver observer = new FileAlterationObserver(filePath, FileFilterUtils.and(FileFilterUtils.fileFileFilter(), FileFilterUtils.suffixFileFilter(".json")));
            FileAlterationListener listener = new FileAlterationListenerAdaptor() {
                @Override
                public void onFileCreate(File file) {
                    operateFirewall("2");
                }

                @Override
                public void onFileChange(File file) {
                    operateFirewall("2");
                }
            };
            observer.addListener(listener);
            monitor.addObserver(observer);
            monitor.start();
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    @RequestMapping("/request")
    public String request(int flag, String param) {
        if (Constants.GET_PROCESS_PORT_INFO != flag) {
            Log.low.info("DataReceiveServer request -> {flag: " + flag + "\tparams: " + param + "}");
        }
        switch (flag) {
            case Constants.GET_OTHER_STORE_INFO:
                return getOtherStoreInfo(param);
            case Constants.GET_PROFILE_WITH_HOST:
                return getProfileWithHost();
            case Constants.GET_HOST_PROC_LOGS:
                return getHostProcLogs(param);
            case Constants.TIME:
                return System.currentTimeMillis() + "";
            case Constants.DOWNLOAD_HOST_PROC_LOG:
                return downloadHostProcLog(param);
            case Constants.BROWSE_HOST_PROC_LOG:
                return browseHostProcLog(param);
            case Constants.GET_HOST_PROC_CONFINGS:
                return getHostProcConfigs(param);
            case Constants.READ_HOST_FILE:
                return readHostFile(param);
            case Constants.WRITE_HOST_FILE:
                return writeHostFile(param);
            case Constants.GET_PROCESSES_INFO:
                return getProcessesInfo(param);
            case Constants.GET_PROCESS_PORT_INFO:
                return getProcessPortInfo(param);
            case Constants.EXEC_SYNC_TIME_HOST:
                return execSyncTimeOneHost(param);
            case Constants.GET_SYNC_TIME_HOST:
                return getSyncTimeOneHost();
            case Constants.GET_SYS_INFO_HOST:
                return getSysInfoOneHost(param);
            case Constants.CLOSE_SERVICE_AGENT:
                return closeServiceAgent();
            case Constants.AGENT_ACCEPT_HEARTBEAT:
                return acceptHeartBeat();
            case Constants.CLOSE_WATCHDOG:
                return closeWatchdog(param);
            case Constants.ADD_INTO_WATCHDOG:
                return addIntoWatchdog(param);
            case Constants.PONG:
                RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
                String pid = runtime.getName().split("@")[0];
                return ReqRespHelper.writeResult(Constants.SUCCESS_RESPONSE_CODE, "pong", pid, new BaseResponse());
            case Constants.MODIFY_CONFIG:
                return modifyConfig(param);
            case Constants.BACKUP_PROGRAM:
                return backupProgram(param);
            case Constants.SOFTWARE_OPERATE:
                return softwareOperate(param);
            case Constants.START_LOGSTASH:
                return startLogStash(param);
            case Constants.CONFIRM_LOGSTASH:
                return confirmLogstash();
            case Constants.OPERATE_FIREWALL:
                return operateFirewall(param);
            case Constants.PID_FILE:
                return getPidFile(param);
            case Constants.PORT_LIST:
                return getPortList(param);
            case Constants.GET_ESLOG_PATH:
                return getEslogPath(param);
            case Constants.EXEC_COMMAND:
                return execCommand(param);
            case Constants.UPDATE_SOFTWARE_INFO:
                try {
                    ArrayList<SoftwareInfo> list = Config.getObjectMapper().readValue(param, new TypeReference<ArrayList<SoftwareInfo>>() {
                    });
                    SoftwareMonitor.INSTANCE.initSoftwareMap(list);
                } catch (Exception e) {
                    Log.high.error("更新程序信息异常", e);
                }
                return null;
            case Constants.CLEAR_RESTART_COUNT:
                return SoftwareMonitor.INSTANCE.clearRestartCount(param);
            case Constants.GET_PRE_DISK_INFO:
                return getPreprocessDiskInfo(param);
            case Constants.UPDATE_LOGSTASH_CONFIG:
                return updateLogstashConfig(param);
            case Constants.SET_HEAP_SIZE:
                return setHeapSize(param);
            case Constants.GET_SOFTWARE_LIST:
                return getSoftwareList(param);
            case Constants.SET_SYSCHRONISED_TIME:
                return setSyschroTime();
            case Constants.PONG_ALL_AGENT:
                return pingOtherAgent(param);
            case Constants.CHECK_DB_BACKUP:
                return checkDbBackup(param);
            case Constants.DELETE_OLD_SOFTWARE:
                return deleteOldSoftware(param);
            case Constants.REPLACE_CONFIG:
                return replaceConfig(param);
            case Constants.GET_FILE_SIZE:
                return getFileSize(param);
            case Constants.PING_AGENT:
                return "PONG";
            default:
                return ReqRespHelper.writeUnknownRequest();
        }
    }

    private String getFileSize(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("backupProgram [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        Long totalSizeOfFilesInDir = 0L;
        try {
            Map backupMap = Config.getObjectMapper().readValue(params, Map.class);
            if (backupMap != null) {
                Object oldFileName = backupMap.get("oldFileName");
                Object path = backupMap.get("remotePath");
                String programName = "";
                if (oldFileName != null && oldFileName instanceof String) {
                    programName = (String) oldFileName;
                }
                String remotePath = "";
                String deployPath = "";
                if (path != null && path instanceof String) {
                    deployPath = (String) path;
                    String osName = System.getProperty("os.name").toLowerCase();
                    if (osName.contains(Constants.OS_WINDOWS)) {
                        remotePath = "D:\\" + deployPath + "\\";
                    } else if (osName.contains(Constants.OS_LINUX)) {
                        remotePath = "/" + deployPath + "/";
                    }
                }
                File oldFile = new File(remotePath + programName);
                totalSizeOfFilesInDir = FileUtil.getTotalSizeOfFilesInDir(oldFile);
            }
        } catch (Exception e) {
            Log.low.error("getFileSize [params:" + params + "] error", e);
            return ReqRespHelper.writeResultFailureWithComtent(e.getMessage());
        }
        return ReqRespHelper.writeSuccessWithContent(totalSizeOfFilesInDir);
    }

    private String replaceConfig(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("backupProgram [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        try {
            Map backupMap = Config.getObjectMapper().readValue(params, Map.class);
            if (backupMap != null) {
                Object oldFileName = backupMap.get("oldFileName");
                Object productVersion = backupMap.get("productVersion");
                Object path = backupMap.get("remotePath");
                Object configPaths = backupMap.get("configPaths");
                String programName = "";
                if (oldFileName != null && oldFileName instanceof String) {
                    programName = (String) oldFileName;
                }
                String version = "";
                if (productVersion != null && productVersion instanceof String) {
                    version = (String) productVersion;
                }
                String remotePath = "";
                String deployPath = "";
                if (path != null && path instanceof String) {
                    deployPath = (String) path;
                    String osName = System.getProperty("os.name").toLowerCase();
                    if (osName.contains(Constants.OS_WINDOWS)) {
                        remotePath = "D:\\" + deployPath + "\\";
                    } else if (osName.contains(Constants.OS_LINUX)) {
                        remotePath = "/" + deployPath + "/";
                    }
                }
                List<String> configPathList = null;
                if (configPaths != null && configPaths instanceof List) {
                    configPathList = (List<String>) configPaths;
                }
                String backupConfigPath = remotePath + "bak" + File.separator + version + File.separator + programName + "_bak" + File.separator;
                String newUploadConfigPath = remotePath + programName + File.separator;
                for (String p : configPathList) {
                    File sourceFile = new File(backupConfigPath + p.replace("/", File.separator));
                    File targetFile = new File(newUploadConfigPath + p.replace("/", File.separator));
                    if (sourceFile.exists() && targetFile.exists()) {
                        if (sourceFile.getName().endsWith(".json") && targetFile.getName().endsWith(".json")) {
                            replaceJsonCommonConfigItem(sourceFile, targetFile);
                        }
                        if (sourceFile.getName().endsWith(".xml") && targetFile.getName().endsWith(".xml")) {
                            replaceXmlCommonConfigItem(sourceFile, targetFile);
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.high.error(e);
            return ReqRespHelper.writeResultFailureWithContent(false);
        }
        return ReqRespHelper.writeSuccessWithContent(true);
    }

    private String replaceStr(Map<String, Object> sourceFileMap, Map<String, Object> targetFileMap, String targetFileStr) {
        if (!MapUtils.isEmpty(sourceFileMap) && !MapUtils.isEmpty(targetFileMap)) {
            for (Map.Entry<String, Object> m : targetFileMap.entrySet()) {
                String key = m.getKey();
                Object o = sourceFileMap.get(key);
                if (m.getValue() instanceof Map) {
                    Map<String, Object> value = (Map<String, Object>) m.getValue();
                    if (value.size() >= 1 && sourceFileMap.get(key) instanceof Map) {
                        Map<String, Object> om = (Map<String, Object>) o;
                        replaceStr(om, value, targetFileStr);
                    } else {
                        if (o != null) {
                            String newStr = o.toString();
                            Integer srartIndex = targetFileStr.indexOf(key) + key.length();
                            Integer endIndex = srartIndex + m.getValue().toString().length();
                            String oldStr = m.getValue().toString();
                            Integer oldStrStartIndex = targetFileStr.indexOf(oldStr);
                            if (oldStrStartIndex > srartIndex && oldStrStartIndex < endIndex) {
                                targetFileStr = targetFileStr.replace(oldStr, newStr);
                            }
                        }
                    }
                }
            }
        }
        return targetFileStr;
    }

    private void replaceXmlCommonConfigItem(File sourceFile, File targetFile) throws IOException {
        Map<String, Object> sourceFileMap = new LinkedHashMap();
        Map<String, Object> targetFileMap = new LinkedHashMap();
        String targetFileStr = FileUtils.readFileToString(targetFile);
        try {
            // 读取xml文件，封装为doc对象
            SAXReader saxreader = new SAXReader();
            SAXReader saxreader2 = new SAXReader();
            Document doc = saxreader.read(sourceFile);
            Document doc2 = saxreader2.read(targetFile);
            Element rootElement = doc.getRootElement();
            Element rootElement2 = doc2.getRootElement();
            // 调用递归方法
            sourceFileMap.put(rootElement.getName(), DiGui(rootElement));
            targetFileMap.put(rootElement2.getName(), DiGui(rootElement2));
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        targetFileStr = replaceStr(sourceFileMap, targetFileMap, targetFileStr);
        FileUtils.writeStringToFile(targetFile, targetFileStr, "UTF-8");
    }

    public Map DiGui(Element rootElement) {
        // 对节点进行判断
        int flag = hasGradeChrid(rootElement);
        // 存储本层的map,采用LinkedHashMap,保证的顺序
        Map<String, Object> map_this = new LinkedHashMap<String, Object>();
        // 存储子节点的map，采用LinkedHashMap,保证的顺序
        Map<String, Object> map_children = new LinkedHashMap<String, Object>();
        // 获取节点迭代器
        Iterator<Element> iterator = rootElement.elementIterator();
        if (flag == 0) {// 说明该节点所有子节点均有子节点,进入递归
            int num = 0;
            while (iterator.hasNext()) {// 依次继续对节点进行操作
                Element childelement = iterator.next();
                map_children = DiGui(childelement);
                map_this.put(childelement.getName() + "_" + num, map_children);
                num++;
            }
        }
        if (flag == 1) {// 说明该节点的所有子节点均无子节点,封装数据
            while (iterator.hasNext()) {
                Element childelement = iterator.next();
                map_this.put(childelement.getName(),
                        (String) childelement.getData());
            }
        }
        if (flag == 2) {// 说明了该节点的子节点有些拥有子节点，有些不拥有
            int nodes = rootElement.elements().size();// 获取子节点个数
            while (nodes >= 1) {
                nodes--;
                int num = 0;//为了让循环重复的节点，避免了key的冲突
                Element element = iterator.next();
                flag = hasGradeChrid(element);//对节点进行判断
                if (flag == 1) {                          //对于子节点，如果只是普通的子节点，那么直接将数进行封装
                    // 封装如map,String,String
                    map_this.put(element.getName(), element.getData());
                } else {                                     //非普通子节点，那么进行递归
                    map_children = DiGui(element);
                    map_this.put(element.getName() + "_" + num, map_children);//为了让循环重复的节点，避免了key的冲突
                }
            }
        }
        return map_this;
    }

    /**
     * 用于判断该节点的类型 0：说明该节点所有子节点均有子节点 1：说明该节点的所有子节点均无子节点 2：说明了该节点的子节点有些拥有子节点，有些不拥有
     *
     * @param rootelement
     * @return
     */
    public int hasGradeChrid(Element rootelement) {
        int flag = 1;// 初始为1，用与处理对没有子节点的节点进行判断
        StringBuffer flag_arr = new StringBuffer();
        Iterator<Element> iterator = rootelement.elementIterator();
        while (iterator.hasNext()) {
            Element element = iterator.next();// 获取入参rootelement节点的子节点
            // Iterator<Element> iterator_chirld = element.elementIterator();
            if (element.elements().size() > 0) {// 判断是否有子节点
                flag_arr.append("0");
            } else {
                flag_arr.append("1");
            }
        }
        // 如果只包含0，说明该节点所有子节点均有子节点
        if (flag_arr.toString().contains("0")) {
            flag = 0;
        }
        // 如果只包含1，说明该节点的所有子节点均无子节点
        if (flag_arr.toString().contains("1")) {
            flag = 1;
        }
        // 如果同时包含了,0,1,说明了该节点的子节点有些拥有子节点，有些不拥有
        if (flag_arr.toString().contains("0")
                && flag_arr.toString().contains("1")) {
            flag = 2;
        }
        return flag;
    }

    private void replaceJsonCommonConfigItem(File sourceFile, File targetFile) throws IOException {
        String sourceFileStr = FileUtils.readFileToString(sourceFile);
        String targetFileStr = FileUtils.readFileToString(targetFile);
        Map<String, Object> sourceFileStrMap = JSON.parseObject(sourceFileStr, new TypeReference<Map<String, Object>>() {
        }.getType());
        Map<String, Object> targetFileStrMap = JSON.parseObject(targetFileStr, new TypeReference<Map<String, Object>>() {
        }.getType());
        targetFileStr = replaceStr(sourceFileStrMap, targetFileStrMap, targetFileStr);
        FileUtils.writeStringToFile(targetFile, targetFileStr, "UTF-8");
    }

    public String checkDbBackup(String params) {
        CheckDbBackupRequest request;
        try {
            request = ObjectMapperUtil.objectMapper.readValue(params, CheckDbBackupRequest.class);
        } catch (IOException e) {
            return ReqRespHelper.writeParamsError();
        }
        File file = new File(request.getBackupPath());
        if (!file.exists()) {
            return ReqRespHelper.writeResultFailureWithComtent(request.getBackupPath() + "不存在");
        }
        File[] files = file.listFiles();
        if (files == null) {
            return ReqRespHelper.writeResultFailureWithComtent(request.getBackupPath() + " 下无备份文件");
        }
        List<MysqlBackupVo> voList = new ArrayList<>(files.length);
        for (File f : files) {
            MysqlBackupVo vo = produceMysqlBackupVoWithBackupFile(f, request.getBackupTime(), request.getBackupType());
            if (vo != null) {
                voList.add(vo);
            }
        }

        return ReqRespHelper.writeSuccessWithContent(voList);
    }

    private MysqlBackupVo produceMysqlBackupVoWithBackupFile(File file, String dirName, int backupType) {
        try (BufferedRandomAccessFile bufferedRandomAccessFile = new BufferedRandomAccessFile(file, "r")) {
            long length = bufferedRandomAccessFile.length();
            if (length < 50) {
                Log.high.error("读取文件" + file.getName() + "长度异常");
                return null;
            }
            bufferedRandomAccessFile.seek(length - 50);
            StringBuilder stringBuilder = new StringBuilder();
            String tmp;
            while ((tmp = bufferedRandomAccessFile.readLine()) != null) {
                stringBuilder.append(tmp);
            }
            MysqlBackupVo mysqlBackupVo = new MysqlBackupVo();
            mysqlBackupVo.setBackupDatabases(file.getName());
            mysqlBackupVo.setBackupTime(dirName);
            mysqlBackupVo.setBackupType(backupType);
            mysqlBackupVo.setSuccess(stringBuilder.toString().contains("Dump completed") ? 1 : 0);
            return mysqlBackupVo;
        } catch (IOException e) {
            Log.high.error("读取备份文件异常", e);
        }
        return null;
    }

    private String setSyschroTime() {
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains(Constants.OS_WINDOWS)) {
            MaintainAgentManager.getInstance().windowsNtp();
        } else if (osName.contains(Constants.OS_LINUX)) {
            MaintainAgentManager.getInstance().linuxNtp();
        }
        return "同步成功!";
    }

    private String[] getResult(Process process) {
        String[] result = new String[2];
        String code = "UTF-8";
        try {
            result[0] = ProcessUtil.readInputStream(process.getInputStream(), code);
            result[1] = ProcessUtil.readInputStream(process.getErrorStream(), code);
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        return result;
    }

    private String getEslogPath(String params) {
        String[] softwares = params.split(",");
        final StringBuilder sb = new StringBuilder();
        for (String software : softwares) {
            SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(software);
            Yaml yaml = new Yaml();
            try {
                Map map = yaml.loadAs(new FileInputStream(new File(softwareInfo.getRealDir() + File.separator + "config" + File.separator + "elasticsearch.yml")), Map.class);
                Object path = map.get("path.logs");
                if (!Objects.isNull(path)) {
                    sb.append(path.toString()).append(File.separator).append(",");
                } else {
                    sb.append(softwareInfo.getRealDir()).append(File.separator).append("logs").append(File.separator).append(",");
                }
            } catch (FileNotFoundException e) {
                Log.high.error("读取 " + software + " 配置文件获取es日志目录失败", e);
            }
        }
        if (sb.length() > 0) {
            sb.setLength(sb.length() - 1);
        }
        return ReqRespHelper.writeSuccessWithContent(sb.toString());
    }

    private String getSoftwareList(String params) {
        Log.low.info("获取目录params : " + params);
        File file;
        if (!Objects.isNull(params)) {
            file = new File(params);
        } else {
            if (ProcessUtil.isWindows()) {
                file = new File("D:\\dist");
            } else {
                file = new File("/dist");
            }
        }
        if (!file.exists()) {
            return ReqRespHelper.writeResultFailureWithComtent("目录不存在");
        } else {
            final File[] files = file.listFiles();
            final LinkedList<String> fileList = new LinkedList<>();
            for (File f : files) {
                if (!f.getName().contains("java-libs") && !f.getName().contains("OpenSSH-Win64")
                        && !f.getName().contains("monitoring") && judgeSoftware(f)) {
                    fileList.add(f.getName());
                }
            }
            try {
                return ReqRespHelper.writeSuccessWithContent(new ObjectMapper().writeValueAsString(fileList));
            } catch (JsonProcessingException e) {
                return ReqRespHelper.writeResultFailureWithComtent("转化json失败");
            }
        }
    }

    private static boolean judgeSoftware(File directory) {
        if (directory.isFile()) {
            if (directory.getName().contains(".sh") || directory.getName().contains(".bat")) {
                return true;
            } else {
                return false;
            }
        } else {
            final String[] names = directory.list();
            if (Objects.isNull(names)) {
                return false;
            }
            for (String name : names) {
                if (name.contains("conf") || name.contains("log") || name.contains("libs") || name.contains("deploy")) {
                    return true;
                }
            }
            return false;
        }
    }

    private String setHeapSize(String params) {
        if (StringUtils.isEmpty(params)) {
            return ReqRespHelper.writeResultFailureWithComtent("请求参数为空");
        }
        try {
            final HashMap hashMap = Config.getObjectMapper().readValue(params, HashMap.class);
            final Object name = hashMap.get("name");
            final Object minSize = hashMap.get("minSize");
            final Object maxSize = hashMap.get("maxSize");
            if (name != null && minSize != null && maxSize != null) {
                SoftwareMonitor.INSTANCE.setHeapSize(name.toString(), Integer.valueOf(minSize.toString()), Integer.valueOf(maxSize.toString()));
            }
            return ReqRespHelper.writeResultSuccess();
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            String msg = e.getMessage() == null ? "设置堆大小失败" : e.getMessage();
            return ReqRespHelper.writeResultFailureWithComtent(msg);
        }
    }

    private String execCommand(String params) {
        try {
            String[] result = ExecUtil.execCmdOnLinux(params);
            return ReqRespHelper.writeSuccessWithContent(result);
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
            return ReqRespHelper.writeResultFailureWithComtent(e.getMessage());
        }
        //  return ReqRespHelper.writeResultSuccess();
    }

    private String getPortList(String params) {
        if (StringUtils.isEmpty(params)) {
            return ReqRespHelper.writeResultFailureWithComtent("请求参数为空");
        }
        String result = MaintainAgentManager.getInstance().getProcessPortInfos(params);
        return result;
    }

    private String getPidFile(String params) {
        if (StringUtils.isEmpty(params)) {
            return ReqRespHelper.writeResultFailureWithComtent("请求参数为空");
        }
        String path = "/proc/" + params;
        File file = new File(path);
        if (file.exists()) {
            return ReqRespHelper.writeSuccessWithContent(file.getAbsoluteFile());
        }
        String msg = "fileNotFound:" + path;
        Log.high.error(msg);
        return ReqRespHelper.writeResultFailureWithComtent(msg);
    }


    private static String operateFirewall(String params) {
        if (StringUtils.isEmpty(params)) {
            return ReqRespHelper.writeResultFailureWithComtent("请求参数为空");
        }

        String command = null;
        switch (params) {
            //开启防火墙
            case "1":
                if (ProcessUtil.isWindows()) {
                    ProcessUtil.execLocalCommand("netsh advfirewall set allprofiles state on");
                } else {
                    ProcessUtil.execLocalCommand("service iptables start");
                    ProcessUtil.execLocalCommand("systemctl start firewalld");
                }
                return ReqRespHelper.writeResultSuccess();
            case "2":
                if (ProcessUtil.isWindows()) {
                    command = "netsh firewall show portopening";
                    String result = ProcessUtil.executeWindows(command);
                    return ReqRespHelper.writeSuccessWithContent(result);
                } else {
                    // command = "iptables -n -L";
                    command = "iptables -L";
                    String result = ProcessUtil.execute(command, false);
                    return ReqRespHelper.writeSuccessWithContent(result);
                }
        }
        return ReqRespHelper.writeResultSuccess();
    }

    // 查找logstash是否存在
    private String confirmLogstash() {
        String logStashPath = "";
        if (!ProcessUtil.isWindows()) {
            logStashPath = "/dist/logstash";
        } else {
            logStashPath = "D:\\dist\\logstash";
        }
        File file = new File(logStashPath);
        if (file.exists()) {
            return ReqRespHelper.writeResultSuccess();
        }
        return ReqRespHelper.writeResultFailure();
    }

    private String startLogStash(String params) {
        return ReqRespHelper.writeResultSuccess();
    }

    private String softwareOperate(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("softwareOperate [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        try {
            OperateTypeStruct operateTypeStruct = Config.getObjectMapper().readValue(params, OperateTypeStruct.class);
            Integer operateType = operateTypeStruct.getOperateType();
            String name = operateTypeStruct.getName();
            //Integer pid = operateTypeStruct.getPid();
            if (operateType == null || StringUtils.isEmpty(name)) {
                return ReqRespHelper.writeResultFailureWithComtent("operateType或name有问题：operateType=》" + operateType + "，name=》" + name);
            }
            String osName = System.getProperty("os.name").toLowerCase();
            //从server获取最新的软件信息
            SoftwareMonitor.INSTANCE.initSoftwareInfo();
            String killCommand = "";
            String startCommand = "";
            String pidFileDir = "";
            final SoftwareInfo soft = SoftwareMonitor.INSTANCE.getSoftwareInfo(name);
            if (soft == null) {
                return ReqRespHelper.writeResult(Constants.SUCCESS_RESPONSE_CODE, killCommand, -1, new BaseResponse());
            }
            Integer pid = soft.getPid(); //不使用传递的pid，改为由agent在操作程序前，来获取server端的最新程序pid
            soft.setOperateType(operateType);
            String dir = soft.getRealDir() + File.separator + soft.getScriptPath();
            String script = soft.getScript();
            if (!SoftwareUtil.startScriptExists(soft, osName)) {
                return ReqRespHelper.writeResult(Constants.FAIL_RESPONSE_CODE, "启动脚本不存在!", null, new BaseResponse());
            }
            if (osName.contains(Constants.OS_WINDOWS)) {
                if (soft.getConfig()) {
                    startCommand = " cd /d \"" + dir + " && " + script;
                } else {
                    startCommand = " cd /d \"" + soft.getRealDir() + "\\deploy\" && start_background.vbs";
                }
                killCommand = "taskkill /f /pid ";
                pidFileDir = soft.getRealDir() + "\\self.pid";
            } else if (osName.contains(Constants.OS_LINUX)) {
                if (soft.getConfig()) {
                    startCommand = "cd " + dir + " && " + script;
                } else {
                    startCommand = "cd " + soft.getRealDir() + "/deploy && chmod 777 start.sh && nohup sh start.sh >/dev/null &";
                }
                killCommand = "kill -9 ";
                pidFileDir = soft.getRealDir() + "/self.pid";
            }
            File pidFile = new File(pidFileDir);
            if (pidFile.exists()) {
                Integer localPid = Integer.valueOf(FileUtils.readFileToString(pidFile, "utf8").trim());
                if (!localPid.equals(pid)) {
                    pid = localPid;
                }
            }
            if (operateType.equals(SoftwareOperateEnum.CLOSE.getId())) {
                pid = SoftwareUtil.closeSoftware(name, pid);
                SoftwareMonitor.INSTANCE.operateSoftware(name, SoftwareOperateEnum.CLOSE.getId());
                return ReqRespHelper.writeResult(Constants.SUCCESS_RESPONSE_CODE, killCommand, pid, new BaseResponse());
            }
            try {
                if (pidFile.exists()) {
                    FileUtils.forceDelete(pidFile);
                }
            } catch (IOException e) {
                Log.high.error(e.getMessage(), e);
            }
            if (operateType.equals(SoftwareOperateEnum.OPEN.getId())) {
                SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(name);
                synchronized (softwareInfo) {
                    Integer newPid;
                    try {
                        newPid = SoftwareUtil.safeOpenSoftware(name);
                    } catch (Exception e) {
                        return ReqRespHelper.writeResultFailureWithComtent("开启程序失败，" + e.getMessage());
                    }
                    SoftwareMonitor.INSTANCE.restartCount(name, OperateTypeEnum.MANUAL);
                    SoftwareMonitor.INSTANCE.operateSoftware(name, SoftwareOperateEnum.OPEN.getId());
                    return ReqRespHelper.writeResult(Constants.SUCCESS_RESPONSE_CODE, startCommand, newPid, new BaseResponse());
                }
            }
            if (operateType.equals(SoftwareOperateEnum.RELOAD.getId())) {
                SoftwareInfo softwareInfo = SoftwareMonitor.INSTANCE.getSoftwareInfo(name);
                synchronized (softwareInfo) {
                    Integer newPid;
                    try {
                        SoftwareUtil.closeSoftware(name, pid);
                        newPid = SoftwareUtil.safeOpenSoftware(name);
                    } catch (Exception e) {
                        return ReqRespHelper.writeResultFailureWithComtent("重启程序失败，" + e.getMessage());
                    }
                    SoftwareMonitor.INSTANCE.restartCount(name, OperateTypeEnum.MANUAL);
                    SoftwareMonitor.INSTANCE.operateSoftware(name, SoftwareOperateEnum.OPEN.getId());
                    if (newPid != null) {
                        return ReqRespHelper.writeResult(Constants.SUCCESS_RESPONSE_CODE, startCommand, newPid, new BaseResponse());
                    }
                }
            }
        } catch (IOException e) {
            ReqRespHelper.writeResultFailureWithComtent(e.getMessage());
        }
        return ReqRespHelper.writeResultSuccess();
    }

    private String modifyConfig(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("modifyConfig [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        CallPythonRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, CallPythonRequest.class);
        } catch (IOException e) {
            Log.low.error("readHostFile [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }
        if (!new File(request.getShellPath()).exists()) {
            return ReqRespHelper.writeResultFailureWithComtent("不存在" + request.getShellPath());
        }
        return MaintainAgentManager.getInstance().callPythonShell(request);
    }


    private String backupProgram(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("backupProgram [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        Boolean result = false;
        try {
            Map backupMap = Config.getObjectMapper().readValue(params, Map.class);
            if (backupMap != null) {
                Object oldFileName = backupMap.get("oldFileName");
                Object productVersion = backupMap.get("productVersion");
                Object path = backupMap.get("remotePath");
                Object ignore = backupMap.get("ignore");
                String programName = "";
                if (oldFileName != null && oldFileName instanceof String) {
                    programName = (String) oldFileName;
                }
                String version = "";
                if (productVersion != null && productVersion instanceof String) {
                    version = (String) productVersion;
                }
                String remotePath = "";
                String deployPath = "";
                if (path != null && path instanceof String) {
                    deployPath = (String) path;
                    String osName = System.getProperty("os.name").toLowerCase();
                    if (osName.contains(Constants.OS_WINDOWS)) {
                        remotePath = "D:\\" + deployPath + "\\";
                    } else if (osName.contains(Constants.OS_LINUX)) {
                        remotePath = "/" + deployPath + "/";
                    }
                }
                String ignoreFile = "";
                if (ignoreFile != null && ignoreFile instanceof String) {
                    ignoreFile = (String) ignore;
                }
                File oldFile = new File(remotePath + programName);
                result = backupFile(remotePath, version, oldFile, ignoreFile);
            }
        } catch (Exception e) {
            Log.low.error("backupProgram [params:" + params + "] error", e);
            return ReqRespHelper.writeResultFailureWithComtent(e.getMessage());
        }
        if (result) {
            return ReqRespHelper.writeSuccessWithContent(true);
        } else {
            return ReqRespHelper.writeResultFailureWithContent(false);
        }
    }

    private Boolean backupFile(String remotePath, String productVersion, File oldFile, String ignoreFile) throws Exception {
        if (StringUtils.isEmpty(remotePath)) {
            return false;
        }
        String separator = File.separator;
        if (oldFile.exists()) {
            String oldFileName = oldFile.getName();
            String bak = remotePath + "bak";
            File bakupFile = new File(bak);
            if (!bakupFile.exists()) {
                bakupFile.mkdirs();
            } else {
                //bak下存存在本次部署的版本号的文件夹且bak文件夹下文件个数大于3
                List<File> files = (List<File>) FileUtils.listFiles(bakupFile, null, false);
                if (files.size() >= 3) {
                    //按版时间排序，只保留最近的两个，删除其他的
                    files.sort(new FileCompareDescUtil());
                    for (int i = 2; i < files.size(); i++) {
                        FileUtil.deleteDir(files.get(i));
                    }
                }
            }
            String bakPath = remotePath + "bak" + separator + productVersion;
            File bakFile = new File(bakPath);
            String backupFilePath = bakPath + separator + oldFileName + "_bak";
            File backupFile = new File(backupFilePath);
            if (!bakFile.exists()) {
                bakFile.mkdirs();
            } else {
                if (backupFile.exists()) {
                    FileUtil.deleteDir(backupFile);
                }
            }
            try {
                if (!StringUtils.isEmpty(ignoreFile)) {
                    ignoreFile = ignoreFile.replace("\\", File.separator).replace("/", File.separator);
                    String[] split = ignoreFile.split(";");
                    for (int i = 0; i < split.length; i++) {
                        File file = new File(remotePath + oldFile + File.separator + split[i]);
                        FileUtils.copyDirectory(oldFile, backupFile, new FileFilter() {
                            @Override
                            public boolean accept(File pathname) {
                                return !pathname.getAbsolutePath().contains(file.getAbsolutePath());
                            }
                        });
                    }
                } else {
                    FileUtils.copyDirectory(oldFile, backupFile);
                }
                Boolean backupResult = checkBackupResult(backupFile);
                if (!backupResult) {
                    return false;
                }
                deleteLogAndSelfPidFile(oldFile);
            } catch (IOException e) {
                return false;
            }
        }
        return true;
    }

    private Boolean checkBackupResult(File backupFile) {
        for (int i = 0; i < 3; i++) {
            long backupFileSize = FileUtil.getTotalSizeOfFilesInDir(backupFile);
            if (backupFileSize > 0) {
                return true;
            } else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }

    protected void deleteLogAndSelfPidFile(File tempFile) {
        File[] files = tempFile.listFiles();
        if (files != null) {
            for (File file : files) {
                String fileName = file.getName();
                if ("log".equals(fileName) || "logs".equals(fileName) || "self.pid".equals(fileName)) {
                    FileUtil.deleteDir(file);
                }
            }
        }
    }

    private String getProcessPortInfo(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("getProcessPortInfo [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        ProcessInfo processInfo;
        try {
            processInfo = Config.getObjectMapper()
                    .readValue(params, ProcessInfo.class);
        } catch (IOException e) {
            Log.low.error("getProcessPortInfo [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }
        String result = MaintainAgentManager.getInstance().getProcessPortInfo(processInfo);
        return result;
    }


    /**
     * 返回程序状态
     * 1.端口占用数量
     * 2.日志中error日志大小
     *
     * @param params
     * @return
     */
    private String getProcessesInfo(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("getProcessesInfo [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        //ProcessInfo request;
        List<ProcessInfo> requests;
        try {
            requests = Config.getObjectMapper()
                    .readValue(params, new TypeReference<List<ProcessInfo>>() {
                    });
        } catch (IOException e) {
            Log.low.error("getProcessesInfo [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }
        return MaintainAgentManager.getInstance().getProcessInfo(requests);
    }

    private String writeHostFile(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("writeHostFile [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        OperateFileRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, OperateFileRequest.class);
        } catch (IOException e) {
            Log.low.error("writeHostFile [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }
        return MaintainAgentManager.getInstance().writeFile(request);
    }

    private String readHostFile(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("readHostFile [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        OperateFileRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, OperateFileRequest.class);
        } catch (IOException e) {
            Log.low.error("readHostFile [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }
        return MaintainAgentManager.getInstance().readFileToString(request);
    }

    private String getHostProcConfigs(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("getHostProcLogs [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        HostProcRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, HostProcRequest.class);
        } catch (IOException e) {
            Log.low.error("getHostProcLogs [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }

        //检查参数字段
        String host = request.getHost();
        String proc = request.getName();
        String path = request.getPath();
        if (DataCheckUtil.isBlank(host)) {
            Log.low.error("getHostProcLogs [host:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (DataCheckUtil.isBlank(proc)) {
            Log.low.error("getHostProcLogs [proc:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (!DataCheckUtil.isIP(host)) {
            Log.low.error("getHostProcLogs unsupported [host:" + host + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }

        if (StringUtils.isEmpty(path)) {
            Log.low.error("getHostProcLogs unsupported [path:" + path + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }
        Log.low.info("getHostProcConfigs params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().getHostProcConfigs(request);
    }

    private String addIntoWatchdog(String params) {
        try {
            ObjectMapper objectMapper = Config.getObjectMapper();
            Map<String, String> map = objectMapper.readValue(params, HashMap.class);
            String watchDogDir = map.get("watchDogDir");
            String exePath = map.get("exePath");
            String workPath = map.get("workPath");
            String ansecProcessKeepDir = watchDogDir + "\\AnsecProcesskeep.ini";
            File ansecProcessKeepFile = new File(ansecProcessKeepDir);
            List<Map<String, Object>> array2 = PropertiesUtil.parseIni(ansecProcessKeepFile);
            List<AnsecProcesskeepVo> ansecProcesskeepList = objectMapper.readValue(objectMapper.writeValueAsString(array2), new TypeReference<List<AnsecProcesskeepVo>>() {
            });
            if (ListUtil.isNotEmpty(ansecProcesskeepList)) {
                AnsecProcesskeepVo vo = new AnsecProcesskeepVo();
                BeanUtils.copyProperties(vo, ansecProcesskeepList.get(0));
                vo.setExePath(exePath);
                vo.setWorkPath(workPath);
                ansecProcesskeepList.add(vo);
            }
        } catch (IOException | IllegalAccessException e) {
            Log.high.error(e.getMessage(), e);
            ReqRespHelper.writeResult(Constants.FAIL_RESPONSE_CODE, Constants.FAILURE + e.getMessage(), null, new BaseResponse());
        } catch (InvocationTargetException e) {
            Log.high.error(e);
        }
        return ReqRespHelper.writeResultSuccess();
    }

    private String closeWatchdog(String params) {
        try {
            String osName = System.getProperty("os.name").toLowerCase();
            if (osName.contains(Constants.OS_LINUX)) {
                ReqRespHelper.writeResult(Constants.FAIL_RESPONSE_CODE, Constants.FAILURE + "暂不支持Linux服务器操作守护狗", null, new BaseResponse());
            }
            ObjectMapper objectMapper = Config.getObjectMapper();
            Map<String, String> map = objectMapper.readValue(params, HashMap.class);
            String tempPath = map.get("tempPath");
            String watchDogDir = map.get("watchDogDir");
            String name = map.get("name");
            String maintainTemp = map.get("maintainTemp");
            String remotePath = map.get("remotePath");
            File tempFile = new File(tempPath);
            String ansecProcessKeepDir = watchDogDir + "\\AnsecProcesskeep.ini";
            File ansecProcessKeepFile = new File(ansecProcessKeepDir);
            List<Map<String, Object>> array2 = PropertiesUtil.parseIni(ansecProcessKeepFile);
            //上传完程序后，需要将该配置回写回去
            AnsecProcesskeepVo resultAnsecProcesskeepVo = null;
            List<AnsecProcesskeepVo> ansecProcesskeepList = objectMapper.readValue(objectMapper.writeValueAsString(array2), new TypeReference<List<AnsecProcesskeepVo>>() {
            });
            List<AnsecProcesskeepVo> tempAnsecProcesskeepList = new ArrayList<>(ansecProcesskeepList);
            if (ListUtil.isNotEmpty(ansecProcesskeepList)) {
                int size = ansecProcesskeepList.size();
                for (int i = 0; i < size; i++) {
                    AnsecProcesskeepVo ansecProcesskeepVo = ansecProcesskeepList.get(i);
                    if (ansecProcesskeepVo == null || StringUtils.isEmpty(ansecProcesskeepVo.getExePath())) {
                        continue;
                    }
                    String exepath = ansecProcesskeepVo.getExePath();
                    if (StringUtils.isNotEmpty(exepath) && exepath.contains(name)) {
                        resultAnsecProcesskeepVo = tempAnsecProcesskeepList.get(i);
                        tempAnsecProcesskeepList.remove(i);
                    }
                }
            }
            if (ListUtil.isNotEmpty(tempAnsecProcesskeepList)) {
                File newAnsecProcesskeepFile = new File("D:\\" + maintainTemp + "\\AnsecProcesskeep.ini");
                if (newAnsecProcesskeepFile.exists()) {
                    newAnsecProcesskeepFile.delete();
                }
                try (BufferedWriter writer2 = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(newAnsecProcesskeepFile), Constants.GBK))) {
                    for (AnsecProcesskeepVo ansecProcesskeepVo : tempAnsecProcesskeepList) {
                        if (ansecProcesskeepVo == null || StringUtils.isEmpty(ansecProcesskeepVo.getExePath())) {
                            continue;
                        }
                        writeAnsecProcessKeep(writer2, ansecProcesskeepVo);
                    }
                    writer2.flush();
                    FileUtils.copyFile(newAnsecProcesskeepFile, ansecProcessKeepFile);
                    //中止守护程序中相应程序的进程失败，报错：拒绝访问
                    //String[] res = ExecUtil.runCmdCommend("taskkill /f /pid " + map.get("pid"));
                    //此处休眠是为了让守护程序能够识别更新后的配置文件,即杀死需要更新的程序的当前进程
                    //此处具体休眠多久待测试，5S偶尔有程序未重启的情况
                    TimeUnit.SECONDS.sleep(8);
                    //将新包传到目的路径
                    File resultFile = new File(remotePath);
                    FileUtils.copyDirectory(tempFile, resultFile);
                    //添加守护
                    writeAnsecProcessKeep(writer2, resultAnsecProcesskeepVo);
                    writer2.flush();
                } catch (IOException e) {
                    Log.high.error(e.getMessage(), e);
                } catch (InterruptedException e) {
                    Log.high.error(e.getMessage(), e);
                }
                FileUtils.copyFile(newAnsecProcesskeepFile, ansecProcessKeepFile);
                newAnsecProcesskeepFile.delete();
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
            ReqRespHelper.writeResult(Constants.FAIL_RESPONSE_CODE, Constants.FAILURE + e.getMessage(), null, new BaseResponse());
        }


        return ReqRespHelper.writeResultSuccess();

    }

    /**
     * 接收心跳
     *
     * @return 返回成功
     */
    private String acceptHeartBeat() {
        return ReqRespHelper.writeResultSuccess();
    }

    /**
     * 获取其他信息中的入库目录大小
     *
     * @param params 相应request的JSON字符串
     * @return 相应response的JSON字符串
     */
    private String getOtherStoreInfo(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("getOtherStoreInfo [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        StoreDirsRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, StoreDirsRequest.class);
        } catch (IOException e) {
            Log.low.error("getOtherStoreInfo [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }

        //检查参数字段
        List<String> storeDirs = request.getStoreDirs();
        if (storeDirs == null || storeDirs.isEmpty()) {
            Log.low.error("getOtherStoreInfo [storeDirs:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        Log.low.info("getOtherStoreInfo params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().getOtherStoreInfo(storeDirs);
    }

    /**
     * 按主机，获取主机和程序的简要信息
     *
     * @return 相应response的JSON字符串
     */
    private String getProfileWithHost() {
        Log.low.info("getProfileWithHost");
        return MaintainAgentManager.getInstance().getProfileWithHost();
    }

    /**
     * 获取某主机中某程序的日志信息
     *
     * @param params 相应request的JSON字符串
     * @return 相应response的JSON字符串
     */
    private String getHostProcLogs(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("getHostProcLogs [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        HostProcRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, HostProcRequest.class);
        } catch (IOException e) {
            Log.low.error("getHostProcLogs [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }

        //检查参数字段
        String host = request.getHost();
        String proc = request.getName();
        String path = request.getPath();
        if (DataCheckUtil.isBlank(host)) {
            Log.low.error("getHostProcLogs [host:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (DataCheckUtil.isBlank(proc)) {
            Log.low.error("getHostProcLogs [proc:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (StringUtils.isEmpty(path)) {
            Log.low.error("getHostProcLogs unsupported [path:" + path + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }

        Log.low.info("getHostProcLogs params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().getHostProcLogs(request);
    }

    /**
     * 下载某主机中某程序的某个日志文件
     *
     * @param params 相应request的JSON字符串
     * @return 相应response的JSON字符串
     */
    private String downloadHostProcLog(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("downloadHostProcLog [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        HostProcFileRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, HostProcFileRequest.class);
        } catch (IOException e) {
            Log.low.error("downloadHostProcLog [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }
        //检查参数字段
        String host = request.getHost();
        String proc = request.getProc();
        String file = request.getFile();
        if (DataCheckUtil.isBlank(host)) {
            Log.low.error("downloadHostProcLog [host:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (DataCheckUtil.isBlank(proc)) {
            Log.low.error("downloadHostProcLog [proc:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (DataCheckUtil.isBlank(file)) {
            Log.low.error("downloadHostProcLog [file:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (!DataCheckUtil.isIP(host)) {
            Log.low.error("downloadHostProcLog unsupported [host:" + host + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }
        if (!BusinessCheckUtil.checkHost(host)) {
            Log.low.error("downloadHostProcLog unsupported [host:" + host + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }
        if (!BusinessCheckUtil.checkUserProc(proc) && !BusinessCheckUtil.checkAgentProc(proc)) {
            Log.low.error("downloadHostProcLog unsupported [proc:" + proc + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }

        Log.low.info("downloadHostProcLog params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().downloadHostProcLog(proc, file);
    }

    /**
     * 浏览某主机中某程序的某个日志文件（最新的部分内容）
     *
     * @param params 相应request的JSON字符串
     * @return 相应response的JSON字符串
     */
    private String browseHostProcLog(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("browseHostProcLog [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        HostProcFileRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, HostProcFileRequest.class);
        } catch (IOException e) {
            Log.low.error("browseHostProcLog [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }

        //检查参数字段
        String host = request.getHost();
        String proc = request.getProc();
        String file = request.getFile();
        if (DataCheckUtil.isBlank(host)) {
            Log.low.error("browseHostProcLog [host:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (DataCheckUtil.isBlank(proc)) {
            Log.low.error("browseHostProcLog [proc:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (DataCheckUtil.isBlank(file)) {
            Log.low.error("browseHostProcLog [file:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }
        if (!DataCheckUtil.isIP(host)) {
            Log.low.error("browseHostProcLog unsupported [host:" + host + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }
        if (!BusinessCheckUtil.checkHost(host)) {
            Log.low.error("browseHostProcLog unsupported [host:" + host + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }
        if (!BusinessCheckUtil.checkUserProc(proc) && !BusinessCheckUtil.checkAgentProc(proc)) {
            Log.low.error("browseHostProcLog unsupported [proc:" + proc + "] error");
            return ReqRespHelper.writeParamsIllegal();
        }

        Log.low.info("browseHostProcLog params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().browseHostProcLog(proc, file);
    }

    /**
     * 单台主机执行时间同步指令
     *
     * @param params 相应request的JSON字符串
     * @return 相应response的JSON字符串
     */
    private String execSyncTimeOneHost(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("execSyncTimeOneHost [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        TimeRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, TimeRequest.class);
        } catch (IOException e) {
            Log.low.error("execSyncTimeOneHost [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }

        //检查参数字段
        long time = request.getTime();
        if (time <= 0) {
            Log.low.error("execSyncTimeOneHost [time:" + time + "] error");
            return ReqRespHelper.writeParamsError();
        }

        Log.low.info("execSyncTimeOneHost params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().execSyncTimeOneHost(time);
    }

    /**
     * 获取单台主机时间同步结果
     *
     * @return 相应response的JSON字符串
     */
    private String getSyncTimeOneHost() {
        Log.low.info("getSyncTimeOneHost");
        return MaintainAgentManager.getInstance().getSyncTimeOneHost();
    }

    /**
     * 获取预处理目录磁盘信息
     */
    private String getPreprocessDiskInfo(String params) {
        Log.low.info("getPreprocessDiskInfo");
        return MaintainAgentManager.getInstance().getPreprocessDiskInfo(params);
    }

    /**
     * 修改logstash配置文件
     */
    private String updateLogstashConfig(String params) {
        Log.low.info("getPreprocessDiskInfo");
        return MaintainAgentManager.getInstance().updateLogstashConfig(params);
    }


    /**
     * 获取单台主机的系统信息
     *
     * @param params 相应request的JSON字符串
     * @return 相应response的JSON字符串
     */
    private String getSysInfoOneHost(String params) {
        //检查传入参数
        if (DataCheckUtil.isBlank(params)) {
            Log.low.error("getSysInfoOneHost [params:null or empty] error");
            return ReqRespHelper.writeParamsEmpty();
        }

        //解析参数
        TypeRequest request;
        try {
            request = Config.getObjectMapper().readValue(params, TypeRequest.class);
        } catch (IOException e) {
            Log.low.error("getSysInfoOneHost [params:" + params + "] error", e);
            return ReqRespHelper.writeParamsError();
        }

        //检查参数字段
        String type = request.getType();
        if (!"profile".equals(type) && !"detail".equals(type)) {
            Log.low.error("getSysInfoOneHost [type:" + type + "] error");
            return ReqRespHelper.writeParamsError();
        }

        Log.low.info("getSysInfoOneHost params:" + params);

        //返回结果
        return MaintainAgentManager.getInstance().getSysInfoOneHost(type);
    }

    /**
     * 关闭服务的Agent
     *
     * @return 相应response的JSON字符串
     */
    private String closeServiceAgent() {
        /*boolean state = MaintainAgentManager.getInstance().closeServiceAgent();
        if (!state) {
            Log.low.error("closeServiceAgent [closeResult:false] error");
            return ReqRespHelper.writeResultFailure();
        }*/
        new Thread(() -> {
            try {
                Thread.sleep(1000L);
                Log.low.info("closeServiceAgent");
                String os = System.getProperty("os.name").toUpperCase();
                if (os.contains("WINDOWS")) {
                    Runtime.getRuntime().exec("net stop MaintainAgent");
                }
                System.exit(1);
            } catch (Exception e) {
                Log.high.error(e.getMessage(), e);
            }
        }).start();
        return ReqRespHelper.writeResultSuccess();
    }


    private void writeAnsecProcessKeep(BufferedWriter writer, AnsecProcesskeepVo ansecProcesskeepVo) throws IOException {
        if (ansecProcesskeepVo == null) {
            return;
        }
        writeData(writer, "[OneItem]");
        String exePath = ansecProcesskeepVo.getExePath() == null ? "" : ansecProcesskeepVo.getExePath();
        writeData(writer, "exepath=" + exePath);
        String workPath = ansecProcesskeepVo.getWorkPath() == null ? "" : ansecProcesskeepVo.getWorkPath();
        writeData(writer, "workpath=" + workPath);
        String keepRunTime = ansecProcesskeepVo.getKeepRuntime() == null ? "" : ansecProcesskeepVo.getKeepRuntime();
        writeData(writer, "keepruntime=" + keepRunTime);
        String cpuLimit = ansecProcesskeepVo.getCpuLimit() == null ? "" : ansecProcesskeepVo.getCpuLimit();
        writeData(writer, "cpulimit=" + cpuLimit);
        String paremter = ansecProcesskeepVo.getParameter() == null ? "" : ansecProcesskeepVo.getParameter();
        writeData(writer, "paremter=" + paremter);
        String hide = ansecProcesskeepVo.getHide() == null ? "" : ansecProcesskeepVo.getHide();
        writeData(writer, "hide=" + hide);
        writer.newLine();
    }

    private void writeData(BufferedWriter writer, String str) throws IOException {
        writer.write(str);
        writer.newLine();
    }

    public String pingOtherAgent(String param) {
        return "";
    }

    private String getLocalIp() {
        if (localIp == null || "".equals(localIp)) {
            localIp = IpUtil.getLocalHost();
        }
        return localIp;
    }

    private String deleteOldSoftware(String params) {
        try {
            Map backupMap = Config.getObjectMapper().readValue(params, Map.class);
            String deployPath = (String) backupMap.get("remotePath");
            String osName = System.getProperty("os.name").toLowerCase();
            String remotePath = "";
            if (osName.contains(Constants.OS_WINDOWS)) {
                remotePath = "D:\\" + deployPath + "\\";
            } else if (osName.contains(Constants.OS_LINUX)) {
                remotePath = "/" + deployPath + "/";
            }
            String oldFileName = (String) backupMap.get("oldFileName");
            List<String> deleteFiles = (List<String>) backupMap.get("deleteFiles");
            String needDeleteFiles = (String) backupMap.get("needDeleteFiles");
            String fullPackage = (String) backupMap.get("fullPackage");
            File file2 = new File(remotePath + oldFileName);
            if (file2.exists()) {
                //删除原文件
                if (!StringUtils.isEmpty(needDeleteFiles)) {
                    needDeleteFiles = needDeleteFiles.replace("\\", File.separator).replace("/", File.separator);
                    String[] split = needDeleteFiles.split(";");
                    for (int i = 0; i < split.length; i++) {
                        File file = new File(remotePath + oldFileName + File.separator + split[i]);
                        if (!file.exists()) {
                            return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, "要删除文件的目标路径为 " + remotePath + split[i] + ",请检查role_service_relation.json文件need_delete_files配置是否正确,文件中need_delete_files项的正确配置应该从程序路径开始"));
                        }
                        try {
                            FileUtil.deleteDir(file);
                        } catch (Exception e) {
                            Log.high.info(e);
                            return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, file.getName()));
                        }
                        Boolean deleteResult = checkDeleteResult(file);
                        if (!deleteResult) {
                            return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, file.getName()));
                        }
                    }
                }
                if ("true".equals(fullPackage)) {
                    File file3 = new File(remotePath + oldFileName);
                    FileUtil.deleteDir(file3);
                    Boolean deleteResult = checkDeleteResult(file3);
                    if (!deleteResult) {
                        return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, file3.getName()));
                    }
                } else {
                    for (int i = 0; i < deleteFiles.size(); i++) {
                        String absolutePath = deleteFiles.get(i);
                        String path = remotePath + absolutePath.substring(absolutePath.indexOf(oldFileName)).replace("\\", File.separator).replace("/", File.separator);
                        File file1 = new File(path);
                        try {
                            FileUtil.deleteDir(file1);
                        } catch (Exception e) {
                            Log.high.info(e);
                            return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, file1.getName()));
                        }
                        Boolean deleteResult = checkDeleteResult(file1);
                        if (!deleteResult) {
                            return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, file1.getName()));
                        }
                    }
                }
            }
            return ReqRespHelper.writeSuccessWithContent(new DeleteResultVo(true, ""));
        } catch (IOException e) {
            Log.high.info(e);
            return ReqRespHelper.writeResultFailureWithContent(new DeleteResultVo(false, ""));
        }
    }

    private Boolean checkDeleteResult(File oldFile) {
        for (int i = 0; i < 3; i++) {
            if (!oldFile.exists()) {
                return true;
            } else {
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }
        return false;
    }
}
