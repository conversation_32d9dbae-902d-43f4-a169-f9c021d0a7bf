package com.maintain.agent.server;

import com.common.log.Log;
import com.common.monitoring.CommonFieldModel;
import com.common.monitoring.MonitoringConfig;
import com.common.monitoring.MonitoringFieldModel;
import com.common.util.ObjectMapperUtil;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.maintain.agent.business.service.HardInfo;
import com.maintain.agent.conf.Config;
import com.maintain.agent.conf.Constants;
import com.maintain.agent.entity.struct.AgentInfo;
import com.maintain.agent.entity.struct.SoftwareInfo;
import com.maintain.agent.entity.struct.SoftwareRestartInfo;
import com.maintain.agent.enums.OperateTypeEnum;
import com.maintain.agent.software.BaseSoftware;
import com.maintain.agent.software.SoftwareList;
import com.maintain.agent.utils.*;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.hyperic.sigar.ProcCpu;
import org.hyperic.sigar.ProcMem;
import org.hyperic.sigar.ProcTime;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2019/7/17
 * 内存
 */
public enum SoftwareMonitor {
    /**
     * 单例
     */
    INSTANCE;

    private volatile boolean init = false;

    /**
     *
     */
    private static final long TIME_OUT = 5 * 60 * 1000L;

    private static final String LINUX_JAR_REG = "\\./libs/[\\w-_\\\\.]+\\.jar";

    private static final String WIN_JAR_REG = "\\.\\\\libs\\\\[\\w-_\\\\.]+\\.jar";

    private static final String PID_REG = "\\d+";

    private static final String WIN_PID_REG = "[0-9]+(?=[^0-9]*$)";

    /**
     * 存放本地所有监控程序的信息
     */
    private volatile Map<String, SoftwareInfo> softwareInfoMap = new ConcurrentHashMap<>();

    /**
     * 程序心跳时间
     */
    private volatile Map<String, Long> softwareHeartBeatTime = new ConcurrentHashMap<>();

    private volatile Map<String, String> softwareStartInfoMap = new ConcurrentHashMap<>();

    public String getSoftwareStartInfo(String software) {
        String startInfo = softwareStartInfoMap.get(software);
        SoftwareInfo softwareInfo = softwareInfoMap.get(software);
        File file = null;
        if (startInfo == null || "".equals(startInfo)) {
            if (ProcessUtil.isWindows()) {
                file = new File(softwareInfo.getRealDir() + "\\deploy\\start.bat");
            } else {
                file = new File(softwareInfo.getRealDir() + "/deploy/start.sh");
            }
            if (file.exists()) {
                try {
                    String content = FileUtils.readFileToString(file, "utf8");
                    Pattern compile;
                    if (ProcessUtil.isWindows()) {
                        compile = Pattern.compile(WIN_JAR_REG);
                    } else {
                        compile = Pattern.compile(LINUX_JAR_REG);
                    }
                    Matcher matcher = compile.matcher(content);
                    if (matcher.find()) {
                        String group = matcher.group(0);
                        softwareStartInfoMap.put(software, group);
                        startInfo = group;
                    }
                } catch (IOException e) {
                    Log.high.error(e.getMessage(), e);
                }
            }
        }
        if (startInfo == null) {
            Log.low.error(software + " 程序的libs信息没有找到");
        }
        return startInfo;
    }

    /**
     * 监控程序
     */
    public void monitorSoftware() {
        if (!init) {
            synchronized (SoftwareMonitor.INSTANCE) {
                if (!init) {
                    try {
                        initSoftwareInfo();
                        init = true;
                    } catch (Exception e) {
                        return;
                    }
                }
            }
        }
        try {
            Set<Map.Entry<String, SoftwareInfo>> entries = softwareInfoMap.entrySet();
            if (entries.size() < 1) {
                return;
            }
            final List<BaseSoftware> softwareManage = SoftwareUtil.softwareManage;
            if (ProcessUtil.isWindows()) {
                Map<String, Set<String>> map = getWindowsCommandResultMap();
                entries.forEach(entry -> {
                    String name = entry.getKey();
                    SoftwareInfo softwareInfo = entry.getValue();
                    if (softwareInfo.getMinHeapSize() != null && softwareInfo.getMaxHeapSize() != null) {
                        try {
                            String heapSizeStr = getHeapSize(name);
                            Integer minHeapSize = Integer.valueOf(heapSizeStr.split(",")[0]);
                            Integer maxHeapSize = Integer.valueOf(heapSizeStr.split(",")[1]);
                            if (!softwareInfo.getMinHeapSize().equals(minHeapSize) && !softwareInfo.getMaxHeapSize().equals(maxHeapSize)) {
                                setHeapSize(name, softwareInfo.getMinHeapSize(), softwareInfo.getMaxHeapSize());
                            }
                        } catch (Exception e) {

                        }
                    }
                    if (softwareInfo.getProgramStatus().equals(1)) {
                        synchronized (softwareInfo) {
                            if (softwareInfo.getProgramStatus().equals(0)) {
                                return;
                            }
                            Set<Integer> pidSet = null;
                            BaseSoftware softwareManageService = null;
                            if (!softwareManage.isEmpty()) {
                                for (BaseSoftware soft : softwareManage) {
                                    if (name.equals(soft.getName())) {
                                        softwareManageService = soft;
                                        break;
                                    }
                                }
                            }

                            if (softwareManageService == null) {
                                pidSet = getWindowsSoftwarePid(map, name);
                                if (Objects.isNull(pidSet) || pidSet.isEmpty()) {
                                    clearSoftwareStartInfo(name);
                                    pidSet = getWindowsSoftwarePid(map, name);
                                }
                            } else {
                                pidSet = softwareManageService.getPidSet();
                            }

                            if (pidSet.isEmpty()) {
                                pidisEmpty(name, softwareInfo);
                            } else if (pidSet.size() < softwareInfo.getProcessCount()) {
                                pidLessProcessCount(name, softwareInfo, pidSet);
                            }
                        }
                    }
                });
            } else {
                //linux程序
                entries.forEach(entry -> {
                    String name = entry.getKey();
                    SoftwareInfo softwareInfo = entry.getValue();
                    if (softwareInfo.getMinHeapSize() != null && softwareInfo.getMaxHeapSize() != null) {
                        try {
                            String heapSizeStr = getHeapSize(name);
                            Integer minHeapSize = Integer.valueOf(heapSizeStr.split(",")[0]);
                            Integer maxHeapSize = Integer.valueOf(heapSizeStr.split(",")[1]);
                            if (!softwareInfo.getMinHeapSize().equals(minHeapSize) && !softwareInfo.getMaxHeapSize().equals(maxHeapSize)) {
                                setHeapSize(name, softwareInfo.getMinHeapSize(), softwareInfo.getMaxHeapSize());
                            }
                        } catch (Exception e) {
                            Log.high.error("计算程序内存出错", e);
                        }
                    }
                    if (softwareInfo.getProgramStatus().equals(1)) {
                        if (softwareInfo.getProgramStatus().equals(0)) {
                            return;
                        }
                        BaseSoftware softwareManageService = null;
                        if (!softwareManage.isEmpty()) {
                            for (BaseSoftware iManageSoftware : softwareManage) {
                                if (name.equals(iManageSoftware.getName())) {
                                    softwareManageService = iManageSoftware;
                                    break;
                                }
                            }
                        }
                        Set<Integer> pidSet;
                        if (softwareManageService == null) {
                            pidSet = getLinuxSoftwarePid(name);
                            if (Objects.isNull(pidSet) || pidSet.isEmpty()) {
                                clearSoftwareStartInfo(name);
                                pidSet = getLinuxSoftwarePid(name);
                            }
                        } else {
                            pidSet = softwareManageService.getPidSet();
                        }
                        if (!pidSet.isEmpty()) {
                            softwareInfo.setPid(pidSet.iterator().next());
                        }
                        if (pidSet.isEmpty()) {
                            pidisEmpty(name, softwareInfo);
                        } else if (pidSet.size() < softwareInfo.getProcessCount()) {
                            pidLessProcessCount(name, softwareInfo, pidSet);
                        }
                        //对开启心跳监控的程序增加数据采集
                        //TODO 可以屏蔽部分程序(我司研发的部分程序会自动生成)
                        if (softwareInfo.getHeartMonitor() == 1) {
                            try {
                                ProcMem procMem = HardInfo.getSigar().getProcMem(softwareInfo.getPid());
                                ProcCpu procCpu = HardInfo.getSigar().getProcCpu(softwareInfo.getPid());


                                ProcTime procTime = HardInfo.getSigar().getProcTime(softwareInfo.getPid());
                                String[] procArgs = HardInfo.getSigar().getProcArgs(softwareInfo.getPid());
                                MonitoringFieldModel model = new MonitoringFieldModel();
                                getLocalIp(model);
                                model.setUsedMemory(procMem.getResident());
                                model.setPid(softwareInfo.getPid().longValue());
                                model.setStartParam(String.join(" ", procArgs));
                                model.setTotalMemory(procMem.getSize());
                                model.setName(softwareInfo.getName());
                                Map<String, Object> info = new HashMap<>();
                                CommonFieldModel commonFieldModel = new CommonFieldModel();
                                commonFieldModel.setCpuPercent(BigDecimal.valueOf(procCpu.getPercent()).setScale(2, 4) + "%");
                                long startTime = procTime.getStartTime();

                                commonFieldModel.setStartTime(startTime <= 0L ? null : com.common.util.DateUtil.getSpecTimestampStr(startTime));
                                info.put("common", commonFieldModel);
                                model.setProgramSize(getProgramSize(new File(softwareInfo.getRealDir())));
                                model.setInfo(info);
                                model.setCapTime(DateUtil.now());
                                writeDataToFile(model);
                            } catch (Exception e) {

                            }
                        }
                    }
                });
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }


    private static Long getProgramSize(File file) {
        if (file != null && file.exists()) {
            if (!file.isDirectory()) {
                return file.length();
            } else {
                File[] files = file.listFiles();
                if (files != null && files.length > 0) {
                    long size = 0L;
                    File[] var4 = files;
                    int var5 = files.length;

                    for(int var6 = 0; var6 < var5; ++var6) {
                        File subFile = var4[var6];
                        size += getProgramSize(subFile);
                    }

                    return size;
                } else {
                    return 0L;
                }
            }
        } else {
            return 0L;
        }
    }

    private static void getLocalIp(MonitoringFieldModel model) throws Exception {
        ArrayList host = new ArrayList();

        try {
            Enumeration e1 = NetworkInterface.getNetworkInterfaces();

            while(e1.hasMoreElements()) {
                NetworkInterface ni = (NetworkInterface)e1.nextElement();
                Enumeration e2 = ni.getInetAddresses();

                while(e2.hasMoreElements()) {
                    InetAddress ip = (InetAddress)e2.nextElement();
                    if (ip != null && ip instanceof Inet4Address && !ip.getHostAddress().equals("127.0.0.1")) {
                        host.add(ip.getHostAddress());
                    }
                }
            }

            model.setHost(ObjectMapperUtil.objectMapper.writeValueAsString(host));
        } catch (SocketException var6) {
            Log.high.error("getLocalIp error," + var6.getMessage());
        }

    }

    private static void writeDataToFile(MonitoringFieldModel model) {
        FileOutputStream fos = null;

        try {
            MonitoringConfig config = MonitoringConfig.getConfig();
            String monitoringDir = config.getMonitoringDirLinux();


            if (StringUtils.isNotEmpty(monitoringDir)) {
                File file = new File(monitoringDir);
                if (!file.exists()) {
                    file.mkdirs();
                }

                String path = monitoringDir + File.separator + model.getName() + "-" + System.currentTimeMillis() + ".json";
                fos = new FileOutputStream(path, false);
                fos.write(ObjectMapperUtil.objectMapper.writeValueAsString(model).getBytes("utf-8"));
            }
        } catch (IOException var14) {
            Log.high.error("write data to file error," + var14.getMessage());
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException var13) {
                    Log.high.error(var13);
                }
            }

        }

    }

    public void pidisEmpty(String name, SoftwareInfo softwareInfo) {
        final SoftwareRestartInfo softwareRestartInfo = new SoftwareRestartInfo();
        Log.low.info(name + " 程序挂了，准备开启...");
        openSoftware(name);
        softwareRestartInfo.setDescription("进程不存在，重启程序");
        softwareRestartInfo.setStatus(2);
        softwareRestartInfo.setSoftwareId(softwareInfo.getId());
        softwareRestartInfo.setCreateTime(new Date());
        requestRestartLog(softwareRestartInfo);
    }

    public void pidLessProcessCount(String name, SoftwareInfo softwareInfo, Set<Integer> pidSet) {
        final SoftwareRestartInfo softwareRestartInfo = new SoftwareRestartInfo();
        Log.low.info(name + "程序挂了 " + (softwareInfo.getProcessCount() - pidSet.size()) + " 个进程，准备重启...");
        SoftwareUtil.closeSoftware(name, pidSet.iterator().next());
        openSoftware(name);
        softwareRestartInfo.setCreateTime(new Date());
        softwareRestartInfo.setSoftwareId(softwareInfo.getId());
        softwareRestartInfo.setDescription("程序进程数 " + pidSet.size() + " ,不满足正常进程数 " + softwareInfo.getProcessCount() + " ,重启程序");
        softwareRestartInfo.setStatus(2);
        requestRestartLog(softwareRestartInfo);
    }

    public void requestRestartLog(SoftwareRestartInfo softwareRestartInfo) {
        try {
            String params = Config.getObjectMapper().writeValueAsString(softwareRestartInfo);

            ServerRequestUtil.request(Constants.RESTART_LOG, params);
        } catch (Exception e) {
            Log.low.error("MaintainServer connect error...", e);
        }
    }

    public void monitorLog() {
        if (!init) {
            synchronized (SoftwareMonitor.INSTANCE) {
                if (!init) {
                    try {
                        initSoftwareInfo();
                        init = true;
                    } catch (Exception e) {
                        return;
                    }
                }
            }
        }
        String logPath;
        if (ProcessUtil.isWindows()) {
            logPath = "\\log";
        } else {
            logPath = "/log";
        }
        dealSoftware(logPath);
    }

    public void monitorStartScript() {
        if (!init) {
            synchronized (SoftwareMonitor.INSTANCE) {
                if (!init) {
                    try {
                        initSoftwareInfo();
                        init = true;
                    } catch (Exception e) {
                        return;
                    }
                }
            }
        }
        final Set<Map.Entry<String, SoftwareInfo>> entries = softwareInfoMap.entrySet();
        for (Map.Entry<String, SoftwareInfo> entry : entries) {
            /*String dir = entry.getValue().getRealDir() + File.separator + entry.getValue().getScriptPath();
            String script = entry.getValue().getScript();
            String filePath;
            if (entry.getValue().getConfig()) {
                filePath = dir + File.separator + script;
            }else {
                if (ProcessUtil.isWindows()) {
                    filePath = entry.getValue().getRealDir() + "\\deploy\\start_background.vbs";
                }else {
                    filePath = entry.getValue().getRealDir() + "/deploy/start_background.vbs";
                }
            }

            File scriptFile = new File(filePath);
            if (scriptFile.exists()) {
                entry.getValue().setIsExists(1);
            }else {
                entry.getValue().setIsExists(0);
            }*/

            if (SoftwareUtil.startScriptExists(entry.getValue(), System.getProperty("os.name").toLowerCase())) {
                entry.getValue().setIsExists(1);
            } else {
                entry.getValue().setIsExists(0);
            }
        }
    }

    public void dealSoftware(String logPath) {
        final Set<Map.Entry<String, SoftwareInfo>> entries = softwareInfoMap.entrySet();
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("GMT+8"));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date start = calendar.getTime();
        for (Map.Entry<String, SoftwareInfo> entry : entries) {
            File logDirectory = new File(entry.getValue().getRealDir() + logPath);
            if (logDirectory.exists() && logDirectory.isDirectory()) {
                File[] errorFiles = logDirectory.listFiles(path -> path.getName().contains("error") && start.getTime() <= path.lastModified());
                entry.getValue().setLogCount(errorFiles == null ? 0 : errorFiles.length);
                //logsatsh占用日志导致硬盘打满; 处理：当程序日志超过100M，则关闭logstash
                File[] errors = logDirectory.listFiles(path -> path.getName().contains("error"));
                File[] logs = logDirectory.listFiles(path -> path.getName().contains("log"));
                List<File> fileList = new LinkedList<>();
                fileList.addAll(Arrays.asList(errors));
                fileList.addAll(Arrays.asList(logs));
                fileList.stream().forEach(e -> {
                    long fileLength = FileUtil.getFileLength(e);
                    // TODO
                    if (fileLength > 100 * 1024 * 1024) {
                        Log.high.info("产生超过：" + fileLength + "M日志");
                        try {
                            final SoftwareInfo soft = SoftwareMonitor.INSTANCE.getSoftwareInfo("logstash");
                            Integer pid = soft.getPid();
                            String osName = System.getProperty("os.name").toLowerCase();
                            String pidFileDir = "";
                            if (osName.contains(Constants.OS_WINDOWS)) {
                                pidFileDir = soft.getRealDir() + "\\self.pid";
                            } else if (osName.contains(Constants.OS_LINUX)) {
                                pidFileDir = soft.getRealDir() + "/self.pid";
                            }
                            File pidFile = new File(pidFileDir);
                            if (pidFile.exists()) {
                                Integer localPid = Integer.valueOf(FileUtils.readFileToString(pidFile, "utf8").trim());
                                if (!localPid.equals(pid)) {
                                    pid = localPid;
                                }
                            }
                            SoftwareUtil.closeSoftware("logstash", pid);
                            if (pidFile.exists()) {
                                FileUtils.forceDelete(pidFile);
                            }
                        } catch (Exception ex) {
                            Log.low.info(ex);
                        }
                    }
                });
            }
            /*if (entry.getValue().getProgramStatus() == 1){
                if (logDirectory.exists() && logDirectory.isDirectory()) {
                    long monitorTime = Config.getSystemConfig().getMonitorTime();
                    File[] logFiles = logDirectory.listFiles(path -> path.getName().contains("log") && (System.currentTimeMillis() - path.lastModified())> Config.getSystemConfig().getMonitorTime());
                    for(File f : logFiles){
                        if (f.exists() && f.isFile()){
                            Log.low.info(entry.getValue().getName() + " 程序超过" + monitorTime + "毫秒未写入日志，认为程序假死，准备重启程序...");
                            SoftwareUtil.closeSoftware(entry.getValue().getName(), entry.getValue().getPid());
                            openSoftware(entry.getValue().getName());
                            final SoftwareRestartInfo softwareRestartInfo = new SoftwareRestartInfo();
                            softwareRestartInfo.setDescription("进程超" + monitorTime + "毫秒未写入日志，程序假死，重启程序");
                            softwareRestartInfo.setStatus(2);
                            softwareRestartInfo.setSoftwareId(entry.getValue().getId());
                            softwareRestartInfo.setCreateTime(new Date());
                            requestRestartLog(softwareRestartInfo);
                        }
                    }
                }
            }*/
        }
    }

    public Map<String, Set<String>> getWindowsCommandResultMap() {
        LinkedHashMap<String, Set<String>> map = new LinkedHashMap<>();
        try {
            String[] strings = ExecUtil.execCmdOnWindows("wmic process where  \"caption like 'java%.exe'\" get commandline,processid");
            Pattern p = Pattern.compile(WIN_PID_REG);
            Pattern p1 = Pattern.compile(WIN_JAR_REG);
            if (strings != null && strings.length > 0) {
                for (String string : strings) {
                    final String[] split = string.split("\n");
                    for (String s : split) {
                        String trim = s.trim();
                        Matcher matcherPid = p.matcher(trim);
                        Matcher matcherJar = p1.matcher(trim);
                        if (matcherJar.find() && matcherPid.find()) {
                            String jar = matcherJar.group(0);
                            Set<String> set = map.get(jar);
                            if (set == null) {
                                set = new LinkedHashSet<>();
                                map.put(jar, set);
                            }
                            set.add(matcherPid.group(0));
                            map.put(jar, set);
                        }
                    }
                }
            }
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
        }
        return map;
    }

    public Set<Integer> getLinuxSoftwarePid(String software) {
        final LinkedHashSet<Integer> set = new LinkedHashSet<>();
        try {
            String[] result = ExecUtil.execCmdOnLinux("jps");
            Log.low.info("执行jps的结果: \n" + result);
            Pattern p = Pattern.compile(PID_REG);
            Matcher matcher;
            if (result != null && result.length > 0) {
                for (String jp : result) {
                    final String[] split = jp.split("\n");
                    for (String js : split) {
                        if (js != null) {
                            final String trim = js.trim();
                            if (trim.contains(software)) {
                                matcher = p.matcher(trim);
                                if (matcher.find()) {
                                    set.add(Integer.valueOf(matcher.group(0)));
                                }
                            }
                        }
                    }
                }
            }
            String command = "ps -ef |grep -v grep| grep \"" + getSoftwareStartInfo(software) + "\"";
            result = ExecUtil.execCmdOnLinux(command);
            Log.low.info("执行" + command + "的结果: \n" + result);
            if (result.length > 0) {
                for (String jp : result) {
                    final String[] split = jp.split("\n");
                    for (String js : split) {
                        if (js != null) {
                            final String trim = js.trim();
                            matcher = p.matcher(trim);
                            if (matcher.find()) {
                                String id = matcher.group(0);
                                set.add(Integer.valueOf(id));
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
        Log.low.info("#{softwareManageService == null时的pidSet:}" + set);
        return set;
    }

    public Set<Integer> getWindowsSoftwarePid(Map<String, Set<String>> commandResut, String software) {
        final LinkedHashSet<Integer> set = new LinkedHashSet<>();
        final Set<String> result = commandResut.keySet();
        String softwareStartInfo;
        for (String s : result) {
            softwareStartInfo = getSoftwareStartInfo(software);
            if (s != null && softwareStartInfo != null && s.contains(softwareStartInfo)) {
                Set<String> pidSet = commandResut.get(s);
                if (pidSet != null && !pidSet.isEmpty()) {
                    for (String s1 : pidSet) {
                        if (s1 != null && !"".equals(s1)) {
                            set.add(Integer.valueOf(s1));
                        }
                    }
                }
            }
        }
        return set;
    }

    public void openSoftware(String software) {
        try {
            SoftwareUtil.safeOpenSoftware(software);
        } catch (Exception e) {
            Log.high.error("守护开启程序 " + software + " 异常", e);
        }
        restartCount(software, OperateTypeEnum.AUTO);
        if (softwareHeartBeatTime.get(software) == null) {
            updateSoftwareHeartBeatTime(software);
        }
    }

    /**
     * 获取程序信息
     *
     * @param software
     * @return
     */
    public SoftwareInfo getSoftwareInfo(String software) {
        return softwareInfoMap.get(software);
    }

    public String clearRestartCount(String software) {
        SoftwareInfo softwareInfo = softwareInfoMap.get(software);
        if (softwareInfo == null) {
            return "0";
        }
        softwareInfo.setRestartCount(0);
        return "1";
    }

    public void initSoftwareInfo() {
        try {

            String responseStr = ServerRequestUtil.request(Constants.GET_SOFTWARE_INFO, Config.getSystemConfig().getMaintainAgent().split(" ")[2]);
            AgentInfo agentInfo = Config.getObjectMapper().readValue(responseStr, AgentInfo.class);
            List<SoftwareInfo> softwareInfos = agentInfo.getSoftwareInfos();
            initSoftwareMap(softwareInfos);
        } catch (Exception e) {
            Log.high.error("初始化程序信息失败", e);
            throw new RuntimeException("初始化程序失败!");
        }
    }

    public void initSoftwareMap(List<SoftwareInfo> list) {
        if (list == null || list.isEmpty()) {
            return;
        }
        softwareInfoMap.clear();
        final SoftwareList softwareManage = SoftwareUtil.softwareManage;
        softwareManage.clear();
        StringBuilder sb = new StringBuilder();
        list.forEach(li -> {
            String name = li.getName();
            softwareInfoMap.put(name, li);
            sb.append(li.toString()).append("\r\n");
            if (li.getConfig()) {
                softwareManage.add(new BaseSoftware(name, li.getKeys().split(","), li.getScriptPath(), li.getScript(), li.getScriptPath(), li.getCloseScript()));
            }
        });
        Log.low.info("当前监听" + list.size() + "个程序：" + sb.toString());
    }

    public void restartCount(String software, OperateTypeEnum operateType) {

        SoftwareInfo softwareInfo = softwareInfoMap.get(software);
        if (softwareInfo == null) {
            return;
        }
        synchronized (softwareInfo) {
            Integer count = softwareInfo.getRestartCount();
            if (count == null) {
                softwareInfo.setRestartCount(1);
            } else {
                softwareInfo.setRestartCount(count + 1);
            }
            softwareInfo.setOperateType(operateType.getValue());
            softwareInfo.setRestartTime(new Date());
        }
    }

    public void operateSoftware(String software, Integer type) {
        try {
            SoftwareInfo softwareInfo = softwareInfoMap.get(software);
            softwareInfo.setProgramStatus(type);
        } catch (Exception e) {
            Log.high.error(e.getMessage(), e);
        }
    }

    /**
     * 更新程序心跳时间
     *
     * @param software
     */
    public void updateSoftwareHeartBeatTime(String software) {
        softwareHeartBeatTime.put(software, System.currentTimeMillis());
    }

    public void clearSoftwareHeartBeatTime(String software) {
        softwareHeartBeatTime.remove(software);
    }

    /**
     * 程序是否心跳超时
     *
     * @param software
     * @return true 超时
     */
    public boolean softwareHeartBeatTimeout(String software) {
        Long time = softwareHeartBeatTime.get(software);
        if (time == null) {
            updateSoftwareHeartBeatTime(software);
            return false;
        } else {
            if ((System.currentTimeMillis() - time) > TIME_OUT) {
                return true;
            } else {
                return false;
            }
        }
    }

    public void sendSoftwareInfo() throws JsonProcessingException {
        Collection<SoftwareInfo> values = softwareInfoMap.values();

        ServerRequestUtil.request(Constants.SENT_SOFTWARE_INFO, Config.getObjectMapper().writeValueAsString(values));
        Log.low.info("发送程序信息给server");
    }

    public void clearSoftwareStartInfo(String software) {
        softwareStartInfoMap.remove(software);
    }

    public void setHeapSize(String software, Integer minSize, Integer maxSize) {
        final SoftwareInfo softwareInfo = softwareInfoMap.get(software);
        softwareInfo.setMinHeapSize(minSize);
        softwareInfo.setMaxHeapSize(maxSize);
        if (softwareInfo.getConfig()) {
            throw new RuntimeException("自动配置启动的程序暂不支持配置堆大小");
        }
        String script;
        if (ProcessUtil.isWindows()) {
            script = softwareInfo.getRealDir() + File.separator + "deploy" + File.separator + "start.bat";
        } else {
            script = softwareInfo.getRealDir() + File.separator + "deploy" + File.separator + "start.sh";
        }
        try {
            final File file = new File(script);
            final String str = FileUtils.readFileToString(file, "utf8");
            String sh;
            if (str.contains("-Xmx")) {
                sh = str.replaceAll("-Xmx[\\w]+", "-Xmx" + maxSize + "m");
            } else {
                int index = str.indexOf("java");
                sh = str.substring(0, index) + "java -Xmx" + maxSize + "m" + str.substring(index + 4);
            }
            if (sh.contains("-Xms")) {
                sh = sh.replaceAll("-Xms[\\w]+", "-Xms" + minSize + "m");
            } else {
                int index = sh.indexOf("java");
                sh = sh.substring(0, index) + "java -Xms" + minSize + "m" + sh.substring(index + 4);
            }
            FileUtils.write(file, sh);
        } catch (IOException e) {
            Log.high.error("set heap size exception", e);
        }
    }

    public String getHeapSize(String software) {
        final SoftwareInfo softwareInfo = softwareInfoMap.get(software);
        if (softwareInfo.getConfig()) {
            return null;
        }
        String path;
        if (ProcessUtil.isWindows()) {
            path = softwareInfo.getRealDir() + File.separator + "deploy" + File.separator + "start.bat";
        } else {
            path = softwareInfo.getRealDir() + File.separator + "deploy" + File.separator + "start.sh";
        }
        try {
            final String str = FileUtils.readFileToString(new File(path), "utf-8");
            Integer maxHeap = extractHeadSizeFromStartContent(str, "-Xmx[\\w]+");
            Integer minHeap = extractHeadSizeFromStartContent(str, "-Xms[\\w]+");
            return minHeap + "," + maxHeap;
        } catch (IOException e) {
            Log.high.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 从启动参数中提取设置的堆栈大小以MB为单位
     *
     * @param content
     * @return
     */
    public Integer extractHeadSizeFromStartContent(String content, String pattern) {
        final Pattern compile = Pattern.compile(pattern);
        final Matcher matcher = compile.matcher(content);
        String temp = null;
        if (matcher.find()) {
            temp = matcher.group(0);
            temp = temp.substring(4);
        }
        if (temp == null) {
            return null;
        }
        if (temp.contains("g") || temp.contains("G")) {
            temp = temp.replaceAll("[a-zA-Z]+", "");
            return Integer.valueOf(temp) * 1024;
        } else if (temp.contains("m") || temp.contains("M")) {
            temp = temp.replaceAll("[a-zA-Z]+", "");
            return Integer.valueOf(temp);
        } else if (temp.contains("T") || temp.contains("t")) {
            temp = temp.replaceAll("[a-zA-Z]+", "");
            return Integer.valueOf(temp) * 1024 * 1024;
        } else if (temp.contains("k") || temp.contains("K")) {
            return Integer.valueOf(temp) / 1024;
        } else {
            return null;
        }
    }
}
